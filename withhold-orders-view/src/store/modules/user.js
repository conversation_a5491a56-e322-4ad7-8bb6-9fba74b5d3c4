import { getToken, setToken, removeToken } from '@/utils/auth'
import {RSAencrypt} from '@/utils/jsEncrypt'

const user = {
  state: {
    token: getToken(),
    name: '',
    username: '',
    phone: '',
    email:'',
    avatar: '',
    roles: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_PHONE: (state,phone) =>{
      state.phone=phone
    },
    SET_EMAIL: (state,email) =>{
      state.email=email
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_USERNAME:(state, username)=>{
      state.username=username
    }
  },

  actions: {


  }
}
export default user
