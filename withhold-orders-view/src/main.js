import Vue from 'vue'


import {
  UTableColumn,
  UTable,
  UxGrid,
  UxTableColumn
} from 'umy-ui';

Vue.use(UTableColumn);
Vue.use(UTable);
Vue.use(UxGrid);
Vue.use(UxTableColumn);

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n

import '@/styles/index.scss' // global css
import 'umy-ui/lib/theme-chalk/index.css';// 引入样式
import App from './App'
import router from './router'
import store from './store'

import '@/icons' // icon
import '@/permission' // permission control
import '@/assets/icon/iconfont.css'//阿里图标库对应CSS样式
//引入element-ui的全部组件
import ElementUI from 'element-ui'
//引入element-ui的css
import 'element-ui/lib/theme-chalk/index.css'
//使用elementUI
import * as filters from './filters' // global filters
import common from "./utils/common";
Vue.use(common)



Vue.use(ElementUI)
Vue.use(Element, { locale })

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

var vm = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
global.vm = vm; //Define you app variable globally
