package com.fb.css.withhold.orders.payroute.handler.route.execute;

import com.alibaba.fastjson.JSON;
import com.fb.css.withhold.orders.api.domain.TWithholdOrderInfo;
import com.fb.css.withhold.orders.api.dto.AgreementInfoPriorityDTO;
import com.fb.css.withhold.orders.api.dto.req.OrderWithholdReq;
import com.fb.css.withhold.orders.api.dto.resp.order.OrderInfoResp;
import com.fb.css.withhold.orders.api.dto.resp.order.OrderWithholdResp;
import com.fb.css.withhold.orders.api.enums.OrderStatusEnum;
import com.fb.css.withhold.orders.payroute.context.PayRouteContext;
import com.fb.css.withhold.orders.payroute.dto.RouteResultDTO;
import com.fb.css.withhold.orders.payroute.entity.PayRouteOrderWithBLOBs;
import com.fb.css.withhold.orders.payroute.enums.PayRouteOrderStatusEnum;
import com.fb.css.withhold.orders.payroute.handler.route.PayRouteHandler;
import com.fb.css.withhold.orders.payroute.process.PayRouteEventPublisher;
import com.fb.css.withhold.orders.payroute.service.inner.PayRouteOrderActionService;
import com.fb.css.withhold.orders.payroute.service.inner.PayRouteOrderInnerService;
import com.fb.css.withhold.orders.payroute.util.PayRouteReqUtil;
import com.fb.css.withhold.orders.service.inner.WithholdOrderInfoInnerService;
import com.fb.css.withhold.orders.service.way.WithholdWayFactory;
import com.fb.framework.base.result.ServiceResult;
import com.fb.framework.core.exception.FbException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

import java.util.Objects;
import java.util.stream.Collectors;

import static com.fb.css.common.constants.NumberConstants.INT_0;
import static com.fb.css.common.constants.NumberConstants.INT_1;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ExecutePayRouteHandler implements PayRouteHandler {

    private final WithholdOrderInfoInnerService withholdOrderInfoInnerService;

    private final PayRouteOrderInnerService payRouteOrderInnerService;

    private final PayRouteOrderActionService payRouteOrderActionService;

    @Override
    public void handle(PayRouteContext context) {

        List<AgreementInfoPriorityDTO> rollingList = context.getPayRouteExtInfo().getRollingList();
        //协议为空和路由检查失败执行兜底策略
        RouteResultDTO routeResultDTO = context.getPayRouteExtInfo().getRouteResult();
        if (routeResultDTO.isRouteResult()){
            //路由检查结束，设置为跳过，下一次轮询直接跳到这个handler
            context.getPayRouteExtInfo().setSkipCheck(Boolean.TRUE);
            //排序优先级和执行顺序
            sortAgreementPriority(context);

            PayRouteOrderWithBLOBs payRouteOrder = context.getPayRouteOrder();
            //获取上一次代扣是否真的发起成功
            TWithholdOrderInfo withHoldOrder = context.getLastWithholdOrder();
            //最后一次路由和代扣完成，终止路由
            if ( payRouteOrder.getRouteNum() >= rollingList.size()
                    && withHoldOrder != null
                    && OrderStatusEnum.valueOf(withHoldOrder.getOrderStatus()).isEndState()){
                //修改为路由终止
                payRouteOrderInnerService.stopRoute(context.getPayRouteExtInfo(), payRouteOrder);
                log.info("支付路由轮询结束,最后一次代扣订单终态,标记路由终止,发布代扣终态事件,id={},rowNum={}",payRouteOrder.getId(),payRouteOrder.getRouteNum());
                OrderInfoResp orderInfoResp = WithholdWayFactory.payResult(withHoldOrder);
                payRouteOrderActionService.sendPayRouteEvent(orderInfoResp,payRouteOrder);
            }else {
                this.execute(context,withHoldOrder);
            }
            //打断执行
            context.interrupt();
        }

    }


    private void sortAgreementPriority(PayRouteContext context ) {
        List<AgreementInfoPriorityDTO> rollingList = context.getPayRouteExtInfo().getRollingList();
        //按照优先级顺序，更新时间倒叙
        rollingList = rollingList.stream()
                .sorted(Comparator.comparing(AgreementInfoPriorityDTO::getPriority))
                .collect(Collectors.toList());
        context.getPayRouteExtInfo().setRollingList(rollingList);
    }

    private  void execute(PayRouteContext context,TWithholdOrderInfo withHoldOrder) {
        PayRouteOrderWithBLOBs payRouteOrder = context.getPayRouteOrder();
        /**
         * 首次路由，用1正常执行
         */
        if (withHoldOrder == null &&  payRouteOrder.getStatus().equals(PayRouteOrderStatusEnum.WAITING.getCode())){
            payRouteOrder.setRouteNum(INT_0);
            this.executeWithhold(context, Boolean.FALSE,payRouteOrder.getStatus());
            return;
        }

        /**
         * 代扣不存在，路由在处理中，进行上一次补偿（这种情况出现在路由更新成功，代扣发起失败的极端情况）
         */
        if (withHoldOrder == null &&  payRouteOrder.getStatus().equals(PayRouteOrderStatusEnum.PROCESSING.getCode())){
            /**
             * 补偿不能升级序列号，不然会跳号，重复扣款,需要用上一次的序号取执行代扣
             */
            log.info("路由订单状态是进行中，代扣不存在,补偿触发代扣补插入：id= {},rowNum={}", payRouteOrder.getId(), payRouteOrder.getRouteNum());
            payRouteOrder.setRouteNum(payRouteOrder.getRouteNum() - INT_1);
            this.executeWithhold(context,Boolean.TRUE,payRouteOrder.getStatus());
            return;
        }
        /**
         * 上一次代扣失败，进行下一次轮询
         */
        if ( (withHoldOrder != null && withHoldOrder.getOrderStatus().equals(OrderStatusEnum.FAILED.getCode()))
                && payRouteOrder.getStatus().equals(PayRouteOrderStatusEnum.PROCESSING.getCode())){
            this.executeWithhold(context ,Boolean.FALSE,payRouteOrder.getStatus());
            return;
        }

        /**
         *  代扣订单存在，并且状态是待处理，处理中，卡件，成功，则无需处理（通过支付回调会完成状态流转，无须处理）
         */
        if (Objects.nonNull(withHoldOrder) && (withHoldOrder.getOrderStatus().equals(OrderStatusEnum.WAITING.getCode())
                || withHoldOrder.getOrderStatus().equals(OrderStatusEnum.PROCESSING.getCode())
                || withHoldOrder.getOrderStatus().equals(OrderStatusEnum.PAUSE.getCode())
                || withHoldOrder.getOrderStatus().equals(OrderStatusEnum.SUCCESS.getCode())) ){
            //路由Waiting 代扣不存在，路由 progress ，代扣执行中
            throw new FbException(String.format("代扣处理中,不应该有补偿任务进入这里,请检查路由轮询补偿路由补偿逻辑：id= %s,rowNum=%s", payRouteOrder.getId(), payRouteOrder.getRouteNum()));
        }
    }



    private void executeWithhold(PayRouteContext context,boolean isRetry,String sourceStatus) {
        OrderWithholdReq req = context.getReq();
        PayRouteOrderWithBLOBs payRouteOrder = context.getPayRouteOrder();
        //构建请求参数
        List<AgreementInfoPriorityDTO> rollingList = context.getPayRouteExtInfo().getRollingList();
        AgreementInfoPriorityDTO currentUseAgreement = rollingList.get(payRouteOrder.getRouteNum());

        payRouteOrder.setPayRouteExtInfo(JSON.toJSONString(context.getPayRouteExtInfo()));
        //设置本次执行的支付协议
        withholdOrderInfoInnerService.handleAgreementInfo(req,currentUseAgreement);
        payRouteOrder.setAgreementInfo(JSON.toJSONString(currentUseAgreement));
        payRouteOrder.setPayChannel(currentUseAgreement.getPayChannel());
        payRouteOrder.setBankCardCode(currentUseAgreement.getBankCardNo());
        payRouteOrder.setStatus(PayRouteOrderStatusEnum.PROCESSING.getCode());
        //使用乐观锁的方式抢占, 这里抢占后，其他定时任务查询查到是增加后的序号，补偿任务可能会重复发起代扣
        if (!isRetry && !payRouteOrderInnerService.preempt(payRouteOrder,sourceStatus)){
            throw new FbException(String.format("路由乐观锁抢占路由序列失败:同序列号并发执行,路由失败：路由单号为：id= %s,rowNum=%s",payRouteOrder.getId(),payRouteOrder.getRouteNum()));
        }
        req.setFrontReqNo(PayRouteReqUtil.getPayRouteReqNo(payRouteOrder.getId(), payRouteOrder.getRouteNum() + INT_1));
        ServiceResult<OrderWithholdResp> withhold = withholdOrderInfoInnerService.withhold(req);
        context.setResp(withhold);
        log.info("路由发起代扣结果：路由单号为：id= {},rowNum={},resp={}",payRouteOrder.getId(),payRouteOrder.getRouteNum(),JSON.toJSONString(withhold));
    }




}
