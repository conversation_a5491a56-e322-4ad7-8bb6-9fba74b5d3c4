package com.fb.spec.finacial.allocation.api.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-01-08 10:26
 */
@Data
public class CssExtTransOrderReq implements Serializable {


    //主键
    private String id;

    //业务订单号
    private String bizOrderId;

    //资金方
    private String capNo;

    //资产方
    private String assetOrgNo;

    //反担保方
    private String counterGuarantor;

    //接入主体
    private String subject;


    //项目编码
    private String projectNo;

    //信托计划编码
    private String trustPlanCode;

    //业务类型：放款 还款 代偿、退款
    private String bizType;

    //是否退款 Y N
    private String isRefundTrans;

    //状态：按业务类型区分：
    //根据业务类型：
    //代偿（有资金流）: 待处理、支付中、支付失败、支付成功
    //代偿（记账）：
    //待处理、代偿成功
    //还款： 待处理、支付中、支付失败、支付成功
    //放款：待处理、放款成功
    //计提：待处理、计提成功
    //退款：待处理、退款中、退款成功、退款失败
    //交易状态
    private String transStatus;

    //创建时间
    private Date createDatetime;

    //更新时间
    private Date updateDatetime;

    //费用信息
    private String feeInfo;

    //页号
    private Integer pageNum;

    //页面大小
    private Integer pageSize;

    /**
     * 创建时间开始
     */
    private Date createDatetimeStr;

    /**
     * 创建时间结束
     */
    private Date createDatetimeEnd;

    /**
     * 更新时间开始
     */
    private Date updateDatetimeStr;

    /**
     * 更新时间结束
     */
    private Date updateDatetimeEnd;




    private static final long serialVersionUID = 1L;
}
