package com.fb.spec.settlement.dao.entity;

import com.fb.framework.base.BaseEntity;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AutoWithdrawalOrder extends BaseEntity implements Serializable {
    /** 主键 */
    private String id;

    /** 项目编码 */
    private String projectNo;

    /** 项目名称 */
    private String projectName;

    /** 提现商户号 */
    private String merchantNo;

    /** 自动提现配置 */
    private String withdrawalConfigId;

    /** 提现订单计划开始时间 */
    private Date schedulerDatetime;

    /** 自动提现订单开始时间 */
    private Date withdrawalStartDatetime;

    /** 自动提现订单完成时间 */
    private Date withdrawalEndDatetime;

    /** 自动提现批次，对应：yyyy-MM-dd */
    private String batchDatetime;

    /** 提现订单序号，默认为：1 */
    private Integer withdrawalNum;

    /** 订单状态 INIT、DOING、FAIL、SUCCESS */
    private String status;

    /** 失败重试标识 Y:已重试 N:未重试 */
    private String retryFlag;

    /** 虚拟户余额 */
    private BigDecimal virtualAccountAmount;

    /** 商户号余额 */
    private BigDecimal amount;

    /** 三方返回码 */
    private String respCode;

    /** 失败原因 */
    private String failReason;

    /** 创建人 */
    private String createdBy;

    /** 更新人 */
    private String updatedBy;

    /** 创建时间 */
    private Date createDatetime;

    /** 更新时间 */
    private Date updateDatetime;

    /**  */
    private Date withdrawalEndDate;

    /** 提现方式(AUTO:自动、MANUAL:手工) */
    private String withdrawalMethod;

    /** 手工提现复核人 */
    private String recheckBy;

    /** 扩展信息 */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}