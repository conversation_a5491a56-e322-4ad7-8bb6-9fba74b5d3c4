package com.fb.spec.settlement.dao.ext;

import com.fb.spec.settlement.dao.entity.AutoWithdrawalDetail;
import com.fb.spec.settlement.dao.entity.SettlementDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExtAutoWithdrawalDetailDao {
    /**翻页获取指定订单的体现明细*/
    List<AutoWithdrawalDetail> queryWithholdDetailListByIdDesc(@Param( "orderId") String orderId,
                                                       @Param( "startId") String  startId,
                                                       @Param("pageSize") Integer pageSize);
    List<AutoWithdrawalDetail> queryWithholdDetailListByIdAsc(@Param( "orderId") String orderId,
                                                       @Param( "startId") String  startId,
                                                       @Param("pageSize") Integer pageSize);
}
