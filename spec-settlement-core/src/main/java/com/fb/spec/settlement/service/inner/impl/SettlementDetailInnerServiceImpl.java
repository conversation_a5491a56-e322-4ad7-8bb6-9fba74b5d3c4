package com.fb.spec.settlement.service.inner.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.fb.css.accounts.api.dto.resp.AccountLogPullResp;
import com.fb.css.common.constants.Constants;
import com.fb.css.common.constants.NumberConstants;
import com.fb.css.common.constants.TransType;
import com.fb.css.common.exception.ServerInnerException;
import com.fb.css.common.util.CssSequenceUtil;
import com.fb.css.common.util.RedisDistributedLockUtils;
import com.fb.css.common.util.ThrowableUtil;
import com.fb.framework.core.config.Property;
import com.fb.spec.settlement.constant.ThreadPoolConstants;
import com.fb.spec.settlement.dao.SettlementDetailDao;
import com.fb.spec.settlement.dao.entity.SettlementDetail;
import com.fb.spec.settlement.dao.ext.ExtSettlementDetailDao;
import com.fb.spec.settlement.dto.SettleDetailUpdateDTO;
import com.fb.spec.settlement.dto.SettlementVarTaskInfoDTO;
import com.fb.spec.settlement.enums.TableIdCode;
import com.fb.spec.settlement.service.inner.SettlementDetailInnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.fb.css.common.constants.NumberConstants.INT_0;
import static com.fb.spec.settlement.constant.ConfigConstants.DUPLICATE_PRIMARY_KEY;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettlementDetailInnerServiceImpl implements SettlementDetailInnerService {

    private static final String LOCK_KEY = "spec.settlement.detail.update.lock";

    private static final String LOCK_TIMEOUT = "spec.settlement.detail.update.lock.timeout";

    private static final String PAGE_SIZE = "spec.settlement.detail.update.query.page.size";

    private static final String RUN_FLAG = "spec.settlement.detail.update.run.flag";

    @Resource
    private SettlementDetailDao settlementDetailDao;

    @Resource
    private ExtSettlementDetailDao extSettlementDetailDao;

    @Resource(name = ThreadPoolConstants.SETTLEMENT_DETAIL_UPDATE_EXECUTOR)
    private ThreadPoolTaskExecutor executor;


    /**
     * 插入结算明细
     *
     * @param taskInfo   变量计算任务信息
     * @param accountLog 账户流水
     */
    @Override
    public void insertSettlementDetail(SettlementVarTaskInfoDTO taskInfo, AccountLogPullResp accountLog) {
        SettlementDetail settlementDetail = new SettlementDetail();
        settlementDetail.setId(CssSequenceUtil.getId(TableIdCode.SEQ_ID_T_SETTLEMENT_DETAIL));
        settlementDetail.setAccountNo(taskInfo.getAccountNo());
        settlementDetail.setConfigId(taskInfo.getConfigId());
        settlementDetail.setSettleTaskId(taskInfo.getSettleTaskId());
        settlementDetail.setSourceId(accountLog.getId());
        settlementDetail.setSettleNum((long) NumberConstants.INT_1);
        String transType = accountLog.getTransType();
        if (TransType.INCOME.name().equals(transType)) {
            settlementDetail.setAmount(accountLog.getIncrBalance());
        }
        if (TransType.EXPENDITURE.name().equals(transType)) {
            settlementDetail.setAmount(accountLog.getDecrBalance().negate());
        }
        settlementDetail.setCompletionTime(accountLog.getCompletionTime());
        settlementDetail.setCreatedBy(Constants.SYS);
        settlementDetail.setUpdatedBy(Constants.SYS);
        try {
            settlementDetailDao.insertSelective(settlementDetail);
        } catch (Exception e) {
            ThrowableUtil.findMybatisDuplicateElseThrow(e,
                    dex -> !dex.getMessage().contains(DUPLICATE_PRIMARY_KEY),
                    r -> log.error("结算明细流水唯一索引冲突：流水重复结算错误, settle task id:{}, source id:{}",
                            taskInfo.getSettleTaskId(), accountLog.getId()));
            throw e;
        }
    }

    /**
     * 失败的时候，更新结算明细的 settle num
     *
     * @param settleDetailUpdate 更新参数
     * @return true: 成功，false: 失败
     */
    @Override
    public boolean updateSettleNumWhenFailed(SettleDetailUpdateDTO settleDetailUpdate) {
        // Redis 锁 settleTaskId，避免重复处理
        String uuid = IdUtil.fastSimpleUUID();
        String lockKey = LOCK_KEY + settleDetailUpdate.getSettleTaskId();

        boolean lock = RedisDistributedLockUtils.tryLock(lockKey, uuid, getLockTimeout());
        if (!lock) {
            return false;
        }
        settleDetailUpdate.setStartId(StringUtils.EMPTY);
        settleDetailUpdate.setFailed(false);

        long settleNum = -System.currentTimeMillis();
        try {
            AtomicInteger semaphore = new AtomicInteger(INT_0);
            // 捞取数据
            while (getRunFlag() && Boolean.FALSE.equals(settleDetailUpdate.getFailed())) {
                List<SettlementDetail> details =
                        extSettlementDetailDao.querySettlementDetailList(settleDetailUpdate.getSettleTaskId(),
                                settleDetailUpdate.getStartId(), getPageSize());
                if (details.isEmpty()) {
                    break;
                }
                SettlementDetail last = CollUtil.getLast(details);
                settleDetailUpdate.setStartId(last.getId());
                doExecute(semaphore, settleDetailUpdate, details, settleNum);
            }
            while (semaphore.get() > INT_0 && getRunFlag()) {
                TimeUnit.MILLISECONDS.sleep(NumberConstants.INT_100);
            }
            return !settleDetailUpdate.getFailed();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("更新结算明细 settle num 线程中断，msg:{}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("更新结算明细 settle num 失败，msg;{}, ex", e.getMessage(), e);
            return false;
        } finally {
            RedisDistributedLockUtils.releaseLock(lockKey, uuid);
        }
    }

    private int getLockTimeout() {
        String timeout = Property.getProperty(LOCK_TIMEOUT,
                String.valueOf(TimeUnit.HOURS.toSeconds(NumberConstants.INT_1)));
        return Integer.parseInt(timeout);
    }

    private int getPageSize() {
        String pageSize = Property.getProperty(PAGE_SIZE, String.valueOf(NumberConstants.INT_100));
        return Integer.parseInt(pageSize);
    }

    private boolean getRunFlag() {
        String runFlag = Property.getProperty(RUN_FLAG, String.valueOf(Boolean.TRUE));
        return Boolean.parseBoolean(runFlag);
    }


    private void doExecute(AtomicInteger semaphore, SettleDetailUpdateDTO settleDetailUpdate,
                           List<SettlementDetail> details,
                           Long settleNum) {
        semaphore.incrementAndGet();
        executor.submit(() -> {
            try {
                List<String> ids = details.stream()
                        .filter(detail -> detail.getSettleNum() > INT_0)
                        .map(SettlementDetail::getId)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ids)) {
                    return;
                }
                int row = extSettlementDetailDao.updateSettleNumWhenFailed(ids, settleNum);
                if (row != ids.size()) {
                    throw new ServerInnerException("更新结算明细 settle num 失败");
                }
            } catch (Exception e) {
                settleDetailUpdate.setFailed(true);
                log.warn("更新结算明细 settle num 失败，msg;{}, ex", e.getMessage(), e);
            } finally {
                semaphore.decrementAndGet();
            }
        });
    }
}
