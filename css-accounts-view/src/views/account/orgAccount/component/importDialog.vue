<template xmlns="http://www.w3.org/1999/html">
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialog" center title="导入机构虚拟账户" destroy-on-close="true" width="20%">
      
      <el-upload 
        class="upload_wrap" 
        ref="upload" 
        :action="baseURL" 
        accept=".xls,.xlsx" 
        :auto-upload="false" 
        :file-list="fileList" 
        :on-progress="onProgress" 
        :on-change="onChange"
        :before-upload="beforeUp"
        :on-success="onSuccess"
        :on-error="onError">
        <el-button slot="trigger" type="text"><i class="el-icon-upload" style="font-size:16px"></i>选择文件</el-button>
        <el-button style="margin-left: 10px;" size="small" type="primary" @click="submitUpload">上传</el-button>
      </el-upload>
    </el-dialog>

  </div>

</template>

<script>
import {add} from "@/api/Account";
import request from "@/utils/request"

export default {
  name: 'importDialog',
  data() {
    return {
      formLabelWidth: '20%',
      dialog: false,
      loading: false,
      submitDisabled: false,
      confirmDialog: false,
      enable: true,
      fileList:[],
      baseURL: ''
    }
  },
  created:function(){
    this.baseURL=request.defaults.baseURL+"/accounts/addBatch"
  },
  methods: {
    openDialog(row) {
     this.dialog = true;
    },
    onChange(file, list) {
  	  if (list.length > 1 && file.status !== "fail") {
        list.splice(0, 1);
      } else if (file.status === "fail") {
        this.$message.error("上传失败，请重新上传！");
        list.splice(0, 1);
      }
    },
    beforeUp(file) {
      const isXls = file.name.split(".")[1] === "xlsx" || file.name.split(".")[1] === "xls";
      if (!isXls) {
        this.$message.error("仅支持上传.xls或.xlsx文件");
	    }
      return isXls;
   },
   submitUpload() {
    this.$refs.upload.submit();
   },
   onSuccess(res){
      this.dialog=false
      if(res.code==200){
        this.$message.success("导入成功！")
      }else if(res.code == 500){
        this.$message.error("导入失败，请检查文件是否正确！");
      }
      this.$emit("reloadData")
   },
  }
}
</script>

<style>
</style>
