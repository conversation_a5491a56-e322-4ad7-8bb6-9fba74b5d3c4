import request from '@/utils/request'

export function resetOrderStatus(data) {
    return request({
        url: '/exceptionOrder/resetOrderStatus',
        method: 'post',
        data: data
    })
}


export function makeOrderTag(data) {
  return request({
    url: '/exceptionOrder/makeOrderTag',
    method: 'post',
    data: data
  })
}

export function createOrderOperateInfo(data) {
  return request({
    url: '/exceptionOrder/createOrderOperateInfo',
    method: 'post',
    data: data
  })
}

export function recheck(data) {
    return request({
      url: '/exceptionOrder/recheck',
      method: 'post',
      data: data
    })
}

