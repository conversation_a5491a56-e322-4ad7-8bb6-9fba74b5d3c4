package com.fb.pay.deduct.baofu.balance;

import com.fb.framework.core.config.Property;

/**
 * <AUTHOR>
 */
public class BaoFuBalanceConstant {

    private BaoFuBalanceConstant() {
    }

    public static final String HEADER = "header";

    public static final String BODY = "body";

    public static final String DEFAULT_VERSION = "4.0.0";
    /**
     * 渠道配置参数的
     */
    public static final String EXT_PARAM_TERMINAL_ID = "terminal_id";

    /**
     * 终端号	terminal_id	M	Max11Numeric
     */
    public static final String TERMINAL_ID = "terminalId";


    /**
     * 报文编号/版本号	version	M	Max7Text	4.0.0.0
     */
    public static final String VERSION = "version";
    /**
     * 商户号	member_id	M	Max11Numeric	宝付提供给商户的唯一编号
     */
    public static final String MEMBER_ID = "memberId";

    public static final String EXT_MEMBER_ID = "member_id";

    /**
     * serviceTp
     */
    public static final String SERVICE_TP = "serviceTp";

    public static final String DEFAULT_SERVICE_TP = "T-1001-006-03";

    /**
     * verifyType
     */
    public static final String VERIFY_TYPE = "verifyType";

    public static final String DEFAULT_VERIFY_TYPE = "1";

    /**
     * accountType
     */
    public static final String ACCOUNT_TYPE = "accountType";

    public static final String DEFAULT_ACCOUNT_TYPE = "ALL";

    /**
     * content
     */
    public static final String CONTENT = "content";


    public static final String RESP_CODE_SUCCESS = "S_0000";

    public static final Integer RET_CODE_SUCCESS = 1;


    public static final String BAO_FU_MERCHANT_BALANCE_HOST = "merchant.balance.baofu.host";
    public static final String DEFAULT_BAO_FU_MERCHANT_BALANCE_HOST = "https://vgw.baofoo.com/union-gw/api/%s/transReq.do";

    public static String getHostUrl(String serviceTp) {
        String host = Property.getProperty(BAO_FU_MERCHANT_BALANCE_HOST, DEFAULT_BAO_FU_MERCHANT_BALANCE_HOST);
        return String.format(host, serviceTp);
    }

    public static final String BAOFU_BALANCE_MAX_IDLE_CONNECTIONS_KEY = "baofu.balance.http.max.idle.connections";

    public static final String BAOFU_BALANCE_KEEP_ALIVE_TIME_KEY = "baofu.balance.http.keep.alive.time";

    public static int getMaxIdleConnections() {
        return Integer.parseInt(Property.getProperty(BAOFU_BALANCE_MAX_IDLE_CONNECTIONS_KEY, "5"));
    }

    public static int getKeepAliveTime() {
        return Integer.parseInt(Property.getProperty(BAOFU_BALANCE_KEEP_ALIVE_TIME_KEY, "30"));
    }

    public static String BAOFU_OK_HTTP_TIMEOUT_KEY = "baofu.balance.okhttp.timeout";

    public static long getOkHttpTimeout() {
        return Long.parseLong(Property.getProperty(BAOFU_OK_HTTP_TIMEOUT_KEY, "5000"));
    }
}
