package com.fb.pay.dao.ext;

import com.fb.pay.api.domain.PayConfig;
import com.fb.pay.api.domain.PayConfigCriteria;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: wuhaibin
 * @Date: 2023/12/28 14:42
 */
public interface ExtPayConfigMapper {
	
	
	/**
	 * 根据查询条件获取配置id的列表
	 *
	 * @param example 查询条件
	 *
	 * @return 配置id的列表
	 */
	List<String> selectIdListByCriteriaAndFbAccessNo(@Param("example") PayConfigCriteria example,
													 @Param("fbAccessNo") String fbAccessNo);
	
	/**
	 * 根据查询条件获取信托计划编码的列表
	 *
	 * @param criteria 查询条件
	 *
	 * @return 配置id的列表
	 */
	List<String> selectTrustNoListByCriteria(PayConfigCriteria criteria);
	
	
	/**
	 * 根据条件更新，如果信托计划编码、业务类型为null，则设置为null
	 * @param record record
	 * @return 更新条数
	 */
	int updateByPrimaryKeySelective(PayConfig record);
	
}
