package com.fb.pay.dao.ext;

import com.fb.pay.api.domain.PayDivideDetail;
import com.fb.pay.api.domain.PayDivideDetailCriteria;
import com.fb.pay.api.dto.OmsDivideResult;
import com.fb.pay.api.dto.req.PayDivideDetailReq;
import com.fb.pay.api.dto.resp.PayDivideDetailResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: wuhaibin
 * @Date: 2023/12/28 14:42
 */
public interface ExtPayDivideDetailMapper {

    List<PayDivideDetailResp> selectByCriteriaByCondition(PayDivideDetailReq record);
	

    List<OmsDivideResult> selectDivideOrderByDay(@Param("example") PayDivideDetailCriteria example,
                                                 @Param("fbAccessNo") String fbAccessNo);

    List<PayDivideDetail> selectByCriteriaAndFbAccessNo(@Param("example") PayDivideDetailCriteria example,
                                                        @Param("fbAccessNo") String fbAccessNo);
	
}
