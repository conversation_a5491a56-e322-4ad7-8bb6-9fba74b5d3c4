package com.fb.pay.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fb.framework.core.config.Property;
import com.fb.framework.core.log.TcLogger;
import com.fb.framework.core.log.TcLoggerFactory;
//import com.fb.framework.mq.MqMessageReq;
//import com.fb.framework.mq.MqProducer;
import com.fb.mysdk.api.MqMessageReq;
import com.fb.mysdk.api.MqProducer;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component()
public class AliyunMessageSender implements AsyncSender {
    private TcLogger logger = TcLoggerFactory.getLogger(AliyunMessageSender.class);

    private String commonTopicName = Property.getProperty("common.topic.name", "commonTopic");


    @Override
    public void sendMQMessage(AsyncMessage asyncMessage) {
        String body = JSON.toJSONString(asyncMessage.getData());
        MqMessageReq msg = new MqMessageReq();
        msg.setTag("pay_pay_payment");
        msg.setBody(body);
        msg.setKey(UUID.randomUUID().toString());
        msg.setTopic(commonTopicName);
        try {
            logger.info("MqSend_send_parms topic: {}, key : {} } type:{}", msg.getTopic(), msg.getKey(), msg.getTag());


            boolean result = MqProducer.sendMessage(msg);
//            SendResult sendResult = producerBean.send(msg);

            // logger.info("MqSend_result key : {} , return result : {}", msg.getKey(), JSONObject.toJSONString(msg));
            logger.info("MqSend_result key : {} , sendResult : {}", msg.getKey(), result);

        } catch (Exception e) {
            logger.info("MqSend_error : {} ", msg.getKey(), e);
            throw e;
        }
    }



    public boolean sendMessage(Object data, String tag) {
        String body = JSON.toJSONString(data);
        MqMessageReq msg = new MqMessageReq();
        msg.setBody(body);
        msg.setKey(UUID.randomUUID().toString());
        msg.setTopic(commonTopicName);
        msg.setTag(tag);

        try {
            return MqProducerPay.sendMessagePay(msg);
        } catch (Exception e) {
            logger.info("MqSend_error : {} ", msg.getKey(), e);
            throw e;
        }
    }

    public boolean sendMessageBytopic(Object data, String topic) {
        String body = JSON.toJSONString(data);
        MqMessageReq msg = new MqMessageReq();
        msg.setBody(body);
        msg.setKey(UUID.randomUUID().toString());
        msg.setTopic(topic);
        try {
            return MqProducerPay.sendMessagePay(msg);
        } catch (Exception e) {
            logger.info("MqSend_error : {} ", msg.getKey(), e);
            throw e;
        }
    }
}
