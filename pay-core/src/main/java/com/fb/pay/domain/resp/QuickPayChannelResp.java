package com.fb.pay.domain.resp;

import com.fb.pay.api.enums.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/6/27 21:47
 * @Describe
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuickPayChannelResp implements Serializable {
    private static final long serialVersionUID = -1784663462108751855L;
    /**三方渠道返回码*/
    private String returnCode;
    /**三方渠道返回信息*/
    private String returnMsg;
    /**订单状态*/
    private OrderStatusEnum orderStatus;
    /**转账终态时间*/
    private Date finishTime;
    /**渠道返回的源串信息*/
    private String channelReturnBody;
    /**渠道订单号*/
    private String channelOrderNo;
}
