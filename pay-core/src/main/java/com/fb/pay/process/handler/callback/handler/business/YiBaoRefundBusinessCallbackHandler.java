package com.fb.pay.process.handler.callback.handler.business;

import com.alibaba.fastjson.JSONObject;
import com.fb.css.common.constants.NumberConstants;
import com.fb.pay.process.context.callback.PayCallbackCommonContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

import static com.fb.pay.constants.CommonPayCallBackConstants.ORDER_AMOUNT;
import static com.fb.pay.constants.YiBaoConstants.CASH_REFUND_FEE;


/**
 * <AUTHOR>
 * @date 2025/2/17 17:07
 * @description 易宝付款回调业务处理类
 */
@Component("yiBaoPayRefundBusinessCallbackHandler")
@Slf4j
public class YiBaoRefundBusinessCallbackHandler extends AbstractYiBaoPayBusinessCallbackHandler {


    private static final String ERROR_MESSAGE = "errorMessage";

    private static final String REFUND_SUCCESS_DATE = "refundSuccessDate";

    private static final String BANK_REFUND_ORDER_ID = "bankRefundOrderId";

    @Override
    protected Date getFinishTime(PayCallbackCommonContext context) {
        JSONObject jsonObject = (JSONObject) context.getRespData();
        return StringUtils.isNotEmpty(jsonObject.getString(REFUND_SUCCESS_DATE)) ? jsonObject.getDate(REFUND_SUCCESS_DATE) : new Date();
    }

    @Override
    protected String getFailureMsg(PayCallbackCommonContext context) {
        JSONObject jsonObject = (JSONObject) context.getRespData();
        return jsonObject.getString(ERROR_MESSAGE);
    }


    @Override
    protected String getTransactionNo(PayCallbackCommonContext context) {
        JSONObject jsonObject = (JSONObject) context.getRespData();
        return jsonObject.getString(BANK_REFUND_ORDER_ID);
    }

    @Override
    protected BigDecimal getActualAmount(PayCallbackCommonContext context) {
        JSONObject jsonObject = (JSONObject) context.getRespData();
        String orderAmount = jsonObject.getString(CASH_REFUND_FEE);
        return new BigDecimal(orderAmount).multiply(new BigDecimal(NumberConstants.INT_100));
    }
}
