package com.fb.pay.process.handler.callback.handler.param;

import com.fb.css.common.exception.IgnoredLogException;
import com.fb.framework.core.context.ServiceContext;
import com.fb.pay.api.dto.req.callback.CallBackReq;
import com.fb.pay.process.context.callback.PayCallbackCommonContext;
import com.fb.pay.process.handler.callback.handler.AbstractPreParamDealHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.fb.pay.constants.CommonPayCallBackConstants.REMARK;

/**
 * <AUTHOR>
 * @Description 通联付款请求参数前置处理
 * @Date 2025/3/4 16:59
 **/
@Service("allinPayPaymentPreParamDealCallbackHandler")
@Slf4j
public class AllinPayPaymentPreParamDealCallbackHandler extends AbstractPreParamDealHandler {


    @Override
    public void handle(PayCallbackCommonContext context) {
        CallBackReq req = context.getReq();
        Map<String, String> extData = req.getRequestParam();
        String fbAccessValue = extData.get(REMARK);
        if (StringUtils.isEmpty(fbAccessValue)) {
            throw new IgnoredLogException("未配置融担编号,走查询补偿方式");
        }
        ServiceContext.getContext().setFbAccessNo(fbAccessValue);
    }

}
