package com.fb.pay.process.handler.callback.handler.notify;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fb.pay.api.domain.PayLog;
import com.fb.pay.api.dto.resp.ChannelResultResp;
import com.fb.pay.process.handler.callback.handler.AbstractResultNotifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

import static com.fb.pay.constants.CommonPayCallBackConstants.*;

/**
 * <AUTHOR>
 * @date 2025/3/12 10:46
 * @description 通联回调通知处理类
 */
@Slf4j
public abstract class AbstractAllInPayResultNotifyHandler extends AbstractResultNotifyHandler {


    @Override
    protected void getChannelResultByQuery(ChannelResultResp channelResultResp, PayLog payLog) {
        JSONObject emptyJson = new JSONObject();
        final String logBody = Optional.ofNullable(payLog.getLogBody()).orElse(JSON_FORMAT);
        final JSONObject jsonObject = JSONUtil.parseObj(logBody);
        final JSONObject info = Optional.ofNullable(jsonObject.getJSONObject(INFO)).orElse(emptyJson);
        final JSONObject trxData = Optional.ofNullable(Optional.ofNullable(jsonObject.getJSONArray(TRX_DATA)).orElse(new JSONArray()).getJSONObject(0)).orElse(emptyJson);
        final JSONObject details = Optional.ofNullable(Optional.ofNullable(trxData.getJSONArray(DETAILS)).orElse(new JSONArray()).getJSONObject(0)).orElse(emptyJson);
        setChannelResultRespInfo(channelResultResp,info.getStr(RET_CODE),info.getStr(ERR_MSG),details.getStr(RET_CODE),details.getStr(ERR_MSG));
    }

    @Override
    protected void getChannelResultByCall(ChannelResultResp channelResultResp, PayLog payLog) {
        String code ="";
        String msg="";
        final JSONObject jsonObject = JSONUtil.parseObj(payLog.getLogBody());
        if (jsonObject.containsKey(CODE)&&jsonObject.containsKey(MSG)) {
            code = Convert.toStr(jsonObject.get(CODE));
            msg =Convert.toStr(jsonObject.get(MSG));
        }else if(jsonObject.containsKey(ALLIN_RET_CODE)&&jsonObject.containsKey(RET_MSG)){
            code = Convert.toStr(jsonObject.get(ALLIN_RET_CODE));
            msg = (String) jsonObject.get(RET_MSG);
        }
        setChannelResultRespInfo(channelResultResp,code,msg,code,msg);
    }

    @Override
    protected void getChannelResultByPayment(ChannelResultResp channelResultResp, PayLog payLog) {
        JSONObject emptyJson = new JSONObject();
        if (StringUtils.isEmpty(payLog.getLogBody())) {
            return;
        }
        final String logBody = Optional.ofNullable(payLog.getLogBody()).orElse(JSON_FORMAT);
        final JSONObject jsonObject = JSONUtil.parseObj(logBody);
        final JSONObject info =  Optional.ofNullable(jsonObject.getJSONObject(INFO)).orElse(emptyJson);
        setChannelResultRespInfo(channelResultResp,info.getStr(RET_CODE),info.getStr(ERR_MSG),info.getStr(RET_CODE),info.getStr(ERR_MSG));
        final JSONObject trxData =  Optional.ofNullable(Optional.ofNullable(jsonObject.getJSONArray(TRX_DATA)).orElse(new JSONArray()).getJSONObject(0)).orElse(emptyJson);
        channelResultResp.setThirdBodyCode(trxData.getStr(RET_CODE));
        channelResultResp.setThirdBodyInfo(trxData.getStr(ERR_MSG));
    }


}
