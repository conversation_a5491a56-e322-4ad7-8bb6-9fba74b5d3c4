package com.fb.pay.utils.exception;

import com.fb.framework.base.BaseCode;

/**
 * <AUTHOR>
 */
public class HttpResponseNullException extends PayException {
    public HttpResponseNullException(BaseCode code) {
        super(code);
    }

    public HttpResponseNullException(BaseCode code, String append) {
        super(code, append);
    }

    public HttpResponseNullException(String msg) {
        super(msg);
    }

    public HttpResponseNullException(String code, String msg) {
        super(code, msg);
    }

    public HttpResponseNullException(String code, String msg, String append) {
        super(code, msg, append);
    }

    public HttpResponseNullException(String code, String msg, Throwable cause) {
        super(code, msg, cause);
    }

    public HttpResponseNullException(Throwable cause) {
        super(cause);
    }

    public HttpResponseNullException(String code, String msg, String append, boolean writableStackTrace, Throwable cause) {
        super(code, msg, append, writableStackTrace, cause);
    }
}
