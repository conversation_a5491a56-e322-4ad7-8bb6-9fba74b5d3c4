package com.fb.pay.deduct.baofu;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fb.framework.core.context.ServiceContext;
import com.fb.framework.core.support.SpringTestCase;
import com.fb.pay.BaseTest;
import com.fb.pay.api.domain.PayChannelAccount;
import com.fb.pay.api.domain.PayConfig;
import com.fb.pay.api.dto.DivideDetailDto;
import com.fb.pay.api.dto.req.PayBankCardDetail;
import com.fb.pay.api.dto.req.PaymentReq;
import com.fb.pay.api.dto.resp.PayResult;
import com.fb.pay.api.dto.resp.PaymentResp;
import com.fb.pay.api.enums.PayChannelEnum;
import com.fb.pay.api.enums.PaySysCode;
import com.fb.pay.api.service.PayHandleService;
import com.fb.pay.api.dto.req.PayBankCardDetail;
import com.fb.pay.api.dto.req.PaymentReq;
import com.fb.pay.api.dto.resp.PayResult;
import com.fb.pay.api.dto.resp.PaymentResp;
import com.fb.pay.api.enums.PayChannelEnum;
import com.fb.pay.api.enums.PaySysCode;
import com.fb.pay.api.service.PayBaoFuCallBackService;
import com.fb.pay.api.service.PayCallBackService;
import com.fb.pay.api.service.PayHandleService;
import com.fb.pay.deduct.baofu.dto.BaoFuChildOrderReq;
import com.fb.pay.deduct.baofu.dto.BaoFuDeductReq;
import com.fb.pay.deduct.baofu.util.FormatUtil;
import com.fb.pay.domain.resp.ThirdPayResp;
import com.fb.pay.task.ConfirmOrderTask;
import com.fb.pay.task.PayBaoFuSingleDivideTask;
import com.fb.scheduler.api.dto.TaskRequest;
import com.github.javafaker.Faker;
import lombok.extern.slf4j.Slf4j;
import com.fb.pay.utils.FlowIdUtil;
import com.github.javafaker.Faker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import static com.fb.pay.service.pay.PayWayAbstract.DateFormat;

@Slf4j
public class BaoFuHttpUtilTest extends BaseTest {
    Faker faker = new Faker(Locale.CHINA);
    String MemberId = "100029078";//商户号
    String TerminalId = "200004282";//终端号
    String motherMemberId = "*********";//商户号
    String motherTerminalId = "200005938";//终端号
    String childMemberId = "*********";//商户号
    String childTerminalId = "*********";//终端号

    String singleMemberId = "*********";//商户号
    String singleTerminalId = "*********";//终端号


    @Autowired
    private PayHandleService payHandleService;

    @Autowired
    private PayBaoFuSingleDivideTask payBaoFuSingleDivideTask;

    @Autowired
    private ConfirmOrderTask confirmOrderTask;


    /**
     * 宝付分账扣款
     */
    @Test
    public void testMotherOrderPayment21() {
        ServiceContext.getContext().setFbAccessNo("xna");
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setDivideType("Y");
        //baoFuDeductReq.setDivideDetail("*********,100;*********,100;");// 100025773   *********
        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.singleOrderPayment(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

    /**
     * 宝付分账扣款
     */
    @Test
    public void testMotherOrderPayment22() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        //baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        BaoFuChildOrderReq baoFuChildOrderReq = new BaoFuChildOrderReq();
        baoFuChildOrderReq.setMemberInfo("*********");
        baoFuChildOrderReq.setTxnAmt(200);
        baoFuChildOrderReqs.add(baoFuChildOrderReq);
        baoFuDeductReq.setBaoFuChildList(baoFuChildOrderReqs);
        baoFuDeductReq.setBaoFuTransId("XNAC042405281747500012080001");
        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.requestPaymentDivideOrderResultQuery(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }


    /**
     * 宝付协议支付查询主单状态
     */
    @Test
    public void testMotherOrderPayment222() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        //baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        BaoFuChildOrderReq baoFuChildOrderReq = new BaoFuChildOrderReq();
        baoFuChildOrderReq.setMemberInfo("*********");
        baoFuChildOrderReq.setTxnAmt(200);
        baoFuChildOrderReqs.add(baoFuChildOrderReq);
        baoFuDeductReq.setBaoFuChildList(baoFuChildOrderReqs);
        baoFuDeductReq.setBaoFuTransId("XNAC042405281747500012080001");
        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.requestPaymentOrderResultQuery(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

    @Test
    public void testPayment12() {
        ServiceContext.getContext().setFbAccessNo("xna");
        PaymentReq paymentReq = new PaymentReq();
        paymentReq.setAssetOrderNo("LOAN714058396249310268");
        paymentReq.setCardAgreementNumber("1202308221449057250009118074");
        paymentReq.setCustomerName("零号张");
        paymentReq.setFrontOrderNo("XNAC092309070947020023700001");
        paymentReq.setFrontReqNo("0035109099688481530548588008959347661512");
        paymentReq.setPayChannelEnum(PayChannelEnum.BAOFU_SINGLE_PAY);
        // 2024-03-19 18:20:11
        paymentReq.setPaymentDate(new Date(2024,3,19 ,18,20));
        paymentReq.setPaymentId("06330638344147704782");
        paymentReq.setProjectNo("zzx-360JR-ykyh");
        paymentReq.setProjectName("中智信-三六零-营口银行");
        paymentReq.setRepaymentAmount(200);
        paymentReq.setTransField("6d5a8c384b0e8386bb424c3c7f6dd89c");
        paymentReq.setSysCode(PaySysCode.CBS);
        paymentReq.setTrustPlanNo("zzx0101");
        paymentReq.setBizType("01");
        paymentReq.setUniqueReqNo("06330638344147704782200392404191820");//06030638344147704782200202403191820

        PayBankCardDetail payBankCardDetail = new PayBankCardDetail();
        payBankCardDetail.setCustomerName("烨伟.尹");
        payBankCardDetail.setBankName("GT45986f41Hy638lrm51v6WzIiKt");
        payBankCardDetail.setCardAgreementNumber("AwesomeSavings752487");
        payBankCardDetail.setBankCardCode("郭 Group");
        payBankCardDetail.setIdCardNumber("304-67-8233");
        payBankCardDetail.setPhoneNumber("***********");
        payBankCardDetail.setCardNumber("6759-1803-7583-1594-489");

        payBankCardDetail.setDivideType("Y");
        List<DivideDetailDto> divideDetailDtoList = new ArrayList<>();
        DivideDetailDto divideDetailDto = new DivideDetailDto();
        DivideDetailDto divideDetailDto1 = new DivideDetailDto();
        divideDetailDto.setMerchantNo("*********");
        divideDetailDto.setAmount("100");
        divideDetailDto1.setMerchantNo("*********");
        divideDetailDto1.setAmount("100");
        divideDetailDtoList.add(divideDetailDto);
        divideDetailDtoList.add(divideDetailDto1);
        payBankCardDetail.setDivideDetailDtoList(divideDetailDtoList);
        paymentReq.setPayBankCardDetail(payBankCardDetail);

        System.out.println("=======================================================");
        System.out.println(JSON.toJSONString(paymentReq));
        System.out.println("=======================================================");

        log.info("划扣测试开始 参数为：{}", JSON.toJSONString(paymentReq));
        final PayResult<PaymentResp> payment = payHandleService.payment(paymentReq);
        System.out.println(JSON.toJSONString(payment));
    }
    @Test
    public void testPayment122() {
        TaskRequest taskRequest = new TaskRequest();
        ServiceContext.getContext().setFbAccessNo("xna");
        payBaoFuSingleDivideTask.executeParallel(taskRequest);
    }

    @Test
    public void testPayment1222() {
        TaskRequest taskRequest = new TaskRequest();
        ServiceContext.getContext().setFbAccessNo("xna");
        confirmOrderTask.executeParallel(taskRequest);
    }

    @Test
    public void testMotherOrderPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        baoFuDeductReq.setAmount("67334");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"200005938\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        BaoFuChildOrderReq baoFuChildOrderReq = new BaoFuChildOrderReq();
        baoFuChildOrderReq.setMemberInfo("*********");
        baoFuChildOrderReq.setTxnAmt(67334);
        baoFuChildOrderReqs.add(baoFuChildOrderReq);
        baoFuDeductReq.setBaoFuChildList(baoFuChildOrderReqs);
        ThirdPayResp thirdPayResp = BaoFuHttpUtil.motherOrderPayment(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

    @Test
    public void testMotherOrderPayment1() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"200005938\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        BaoFuChildOrderReq baoFuChildOrderReq = new BaoFuChildOrderReq();
        baoFuChildOrderReq.setMemberInfo("*********");
        baoFuChildOrderReq.setTxnAmt(200);
        baoFuChildOrderReqs.add(baoFuChildOrderReq);
        baoFuDeductReq.setBaoFuChildList(baoFuChildOrderReqs);
        ThirdPayResp thirdPayResp = BaoFuHttpUtil.motherOrderPayment(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

    @Test
    public void testQueryOrder(){
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID13E9DD813BD6C8022C981B77E0");
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"200005938\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        BaoFuChildOrderReq baoFuChildOrderReq = new BaoFuChildOrderReq();
        baoFuChildOrderReq.setMemberInfo("*********");
        baoFuChildOrderReq.setTxnAmt(200);
        baoFuChildOrderReqs.add(baoFuChildOrderReq);
        baoFuDeductReq.setBaoFuChildList(baoFuChildOrderReqs);
        ThirdPayResp thirdPayResp = BaoFuHttpUtil.queryOrder(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }





    /**
     * 子订单
     */
    @Test
    public void testPayment1() {

        PaymentReq paymentReq = new PaymentReq();
        paymentReq.setAssetOrderNo("LOAN714058396249310268");
        paymentReq.setCardAgreementNumber("1202308221449057250009118074");
        paymentReq.setCustomerName("零号张");
        paymentReq.setFrontOrderNo("XNAC092309070947020023700001");
        paymentReq.setFrontReqNo("0035109099688481530548588008959347661512");
        paymentReq.setPayChannelEnum(PayChannelEnum.BAOFU_CHILD_PAY);
        // 2024-03-19 18:20:11
        paymentReq.setPaymentDate( new Date("1710843611000"));
        paymentReq.setPaymentId("06030638344147704782");
        paymentReq.setProjectNo("zzx-360JR-zalfyh");
        paymentReq.setProjectName("中智信-三六零-众安廊坊银行");
        paymentReq.setRepaymentAmount(10002);
        paymentReq.setTransField("6d5a8c384b0e8386bb424c3c7f6dd89c");
        paymentReq.setUniqueReqNo("0603063834414770478010000202308312321");
        paymentReq.setSysCode(PaySysCode.CBS);
        paymentReq.setTrustPlanNo("zzx0101");
        paymentReq.setBizType("01");
        paymentReq.setUniqueReqNo("0603063834414770478210002202403191820");

        PayBankCardDetail payBankCardDetail = new PayBankCardDetail();
        payBankCardDetail.setCustomerName("烨伟.尹");
        payBankCardDetail.setBankName("GT45986f41Hy638lrm51v6WzIiKt");
        payBankCardDetail.setCardAgreementNumber("AwesomeSavings752487");
        payBankCardDetail.setBankCardCode("郭 Group");
        payBankCardDetail.setIdCardNumber("304-67-8233");
        payBankCardDetail.setPhoneNumber("***********");
        payBankCardDetail.setCardNumber("6759-1803-7583-1594-489");

        paymentReq.setPayBankCardDetail(payBankCardDetail);


        log.info("划扣测试开始 参数为：{}", JSON.toJSONString(paymentReq));
        final PayResult<PaymentResp> payment = payHandleService.payment(paymentReq);
        System.out.println(JSON.toJSONString(payment));
    }


    /**
     * 子支付 扣款
     */
    @Test
    public void testChildOrderPaymentPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");

        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));
        System.out.println(JSONUtil.toJsonStr(baoFuDeductReq.getBaoFuTransId()));
        //baoFuDeductReq.setBaoFuTransId("TIDBF69008B18ED7C0769F2B7AE5B");
        baoFuDeductReq.setMainMemberId("*********");
        baoFuDeductReq.setMainPayOrderNo("TID8EEB5EE54C9B359C2AA0BD6FFA");

        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);

        ThirdPayResp thirdPayResp = BaoFuChildHttpUtil.childOrderPayment(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

    /**
     * 子支付 扣款
     */
    @Test
    public void testChildOrderPaymentPayment2() {

        PaymentReq paymentReq = new PaymentReq();
        paymentReq.setAssetOrderNo("LOAN714058396249310268");
        paymentReq.setCardAgreementNumber("1202308221449057250009118074");
        paymentReq.setCustomerName("零号张");
        paymentReq.setFrontOrderNo("XNAC092309070927020023700002");
        paymentReq.setFrontReqNo("0035109099688481530548528008959347661512");
        paymentReq.setPayChannelEnum(PayChannelEnum.BAOFU_CHILD_PAY);
        // 2024-03-19 18:20:11
        paymentReq.setPaymentDate( new Date(1713520202000L));
        paymentReq.setPaymentId("06030638344147704782");
        paymentReq.setProjectNo("hf-ppd-dyyhfr");
        paymentReq.setProjectName("汇丰-拍拍贷-东营银行");
        paymentReq.setRepaymentAmount(200);
        paymentReq.setTransField("6d5a8c384b0e8386bb424c3c7f6dd89c");
        paymentReq.setUniqueReqNo("060306383441477047822002404191742");
        paymentReq.setSysCode(PaySysCode.CBS);
        //paymentReq.setTrustPlanNo("zzx0101");
        //paymentReq.setBizType("01");
        paymentReq.setUniqueReqNo("06030638344147704782200202404191750");
        PayBankCardDetail payBankCardDetail = new PayBankCardDetail();
        payBankCardDetail.setCustomerName("烨伟.尹");
        payBankCardDetail.setBankName("GT45986f41Hy638lrm51v6WzIiKt");
        payBankCardDetail.setCardAgreementNumber("AwesomeSavings752487");
        payBankCardDetail.setBankCardCode("郭 Group");
        payBankCardDetail.setIdCardNumber("304-67-8233");
        payBankCardDetail.setPhoneNumber("***********");
        payBankCardDetail.setCardNumber("6759-1803-7583-1594-489");
        paymentReq.setMainMemberId("*********");
        paymentReq.setMainPayOrderNo("TIDF42AE3E330E00EF2174C44C5B9");
        paymentReq.setPayBankCardDetail(payBankCardDetail);
        log.info("划扣测试开始 参数为：{}", JSON.toJSONString(paymentReq));
        final PayResult<PaymentResp> payment = payHandleService.payment(paymentReq);
        System.out.println(JSON.toJSONString(payment));
    }

    /**
     * 子支付 查询
     */
    @Test
    public void testQuerySingleOrderPaymentPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        //baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setBaoFuTransId("TID58934DAAA10335297CB04D299F");
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));

        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);
        ThirdPayResp thirdPayResp = BaoFuChildHttpUtil.requestChildPaymentOrderResultQuery(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }
    /**
     * 子支付 查询
     */
    @Test
    public void testCallBackSingleOrderPaymentPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");

        ThirdPayResp thirdPayResp = BaoFuChildHttpUtil.requestChildPaymentOrderResultQuery(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

       /*@Test
    public void testSingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));

        baoFuDeductReq.setPaymentRequestId("TID0551C7414A0C6A24F6E803AA46");// 扣款的原始流水号
        baoFuDeductReq.setRefundRequestId("*****************************");// 请求流水号
        baoFuDeductReq.setRequestSerialNumber(FlowIdUtil.creatPayOrder());// 请求流水号

        baoFuDeductReq.setAmount("100");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);
        PayChannelAccount payChannelAccount = new PayChannelAccount();

        baoFuDeductReq.setPayChannelAccount(payChannelAccount);
        ThirdPayResp thirdPayResp = BaoFuHttpUtil.singleOrderRefund(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }*/



    /*@Test
    public void testQuerySingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));


        baoFuDeductReq.setPaymentRequestId("TID0551C7414A0C6A24F6E803AA46");// 扣款的原始流水号
        baoFuDeductReq.setRefundRequestId("*****************************");// 请求流水号
        baoFuDeductReq.setRequestSerialNumber(FlowIdUtil.creatPayOrder());// 请求流水号


        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        baoFuDeductReq.setPayConfig(payConfig);

        ThirdPayResp thirdPayResp = BaoFuHttpUtil.requestChildPaymentOrderResultQuery(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }*/





    /*@Test
    public void testCallBackSingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");

        ThirdPayResp thirdPayResp = BaoFuHttpUtil.singleOrderRefund(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }*/


    @Test
    public void testPayment11() {

        PaymentReq paymentReq = new PaymentReq();
        paymentReq.setAssetOrderNo("LOAN714058396249310268");
        paymentReq.setCardAgreementNumber("1202308221449057250009118074");
        paymentReq.setCustomerName("零号张");
        paymentReq.setFrontOrderNo("XNAC092309070947020023700001");
        paymentReq.setFrontReqNo("0035109099688481530548588008959347661512");
        paymentReq.setPayChannelEnum(PayChannelEnum.BAOFU_SINGLE_PAY);
        // 2024-03-19 18:20:11
        paymentReq.setPaymentDate( new Date("1710843611000"));
        paymentReq.setPaymentId("06030638344147704782");
        paymentReq.setProjectNo("zzx-360JR-zalfyh");
        paymentReq.setProjectName("中智信-三六零-众安廊坊银行");
        paymentReq.setRepaymentAmount(10002);
        paymentReq.setTransField("6d5a8c384b0e8386bb424c3c7f6dd89c");
        paymentReq.setUniqueReqNo("0603063834414770478010000202308312321");
        paymentReq.setSysCode(PaySysCode.CBS);
        paymentReq.setTrustPlanNo("zzx0101");
        paymentReq.setBizType("01");
        paymentReq.setUniqueReqNo("0603063834414770478210002202403191820");

        PayBankCardDetail payBankCardDetail = new PayBankCardDetail();
        payBankCardDetail.setCustomerName("烨伟.尹");
        payBankCardDetail.setBankName("GT45986f41Hy638lrm51v6WzIiKt");
        payBankCardDetail.setCardAgreementNumber("AwesomeSavings752487");
        payBankCardDetail.setBankCardCode("郭 Group");
        payBankCardDetail.setIdCardNumber("304-67-8233");
        payBankCardDetail.setPhoneNumber("***********");
        payBankCardDetail.setCardNumber("6759-1803-7583-1594-489");

        paymentReq.setPayBankCardDetail(payBankCardDetail);


        log.info("划扣测试开始 参数为：{}", JSON.toJSONString(paymentReq));
        final PayResult<PaymentResp> payment = payHandleService.payment(paymentReq);
        System.out.println(JSON.toJSONString(payment));
    }



    @Test
    public void testSingleOrderPaymentPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));

        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);

        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.singleOrderPayment(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }


    @Test
    public void testSingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));

        baoFuDeductReq.setPaymentRequestId("TID0551C7414A0C6A24F6E803AA46");// 扣款的原始流水号
        baoFuDeductReq.setRefundRequestId("*****************************");// 请求流水号
        baoFuDeductReq.setRequestSerialNumber(FlowIdUtil.creatPayOrder());// 请求流水号

        baoFuDeductReq.setAmount("100");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);
        PayChannelAccount payChannelAccount = new PayChannelAccount();

        baoFuDeductReq.setPayChannelAccount(payChannelAccount);
        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.singleOrderRefund(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }




    /*@Test
    public void testQuerySingleOrderPaymentPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        //baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setBaoFuTransId("TID0551C7414A0C6A24F6E803AA46");
        baoFuDeductReq.setRequestSerialNumber("TI2"+ FormatUtil.CreateAeskey(26));

        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        baoFuDeductReq.setPayConfig(payConfig);
        ThirdPayResp thirdPayResp = BaoFuHttpUtil.querySingleOrder(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }


    @Test
    public void testQuerySingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));


        baoFuDeductReq.setPaymentRequestId("TID0551C7414A0C6A24F6E803AA46");// 扣款的原始流水号
        baoFuDeductReq.setRefundRequestId("*****************************");// 请求流水号
        baoFuDeductReq.setRequestSerialNumber(FlowIdUtil.creatPayOrder());// 请求流水号


        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");
        ArrayList<BaoFuChildOrderReq> baoFuChildOrderReqs = new ArrayList<>();
        baoFuDeductReq.setPayConfig(payConfig);

        ThirdPayResp thirdPayResp = BaoFuHttpUtil.queryRefundOrder(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }*/


    @Autowired
    private PayBaoFuCallBackService payBaoFuCallBackService;




    @Test
    public void testCallBackSingleOrderRefundPayment() {
        BaoFuDeductReq baoFuDeductReq = new BaoFuDeductReq();
        baoFuDeductReq.setAgreementNumber("1202308221449057250009118074");
        baoFuDeductReq.setBaoFuTransId("TID"+ FormatUtil.CreateAeskey(26));
        baoFuDeductReq.setAmount("200");
        PayConfig payConfig = new PayConfig();
        payConfig.setChannelExt("[{\"key\":\"member_id\",\"value\":\"*********\",\"desc\":\"商户号\"},{\"key\":\"terminal_id\",\"value\":\"*********\",\"desc\":\"终端号\"},{\"key\":\"SIGN_PASSWORD\",\"value\":\"123456\",\"desc\":\"签名密码\"}]");
//        baoFuDeductReq.setPayConfig(payConfig);
        baoFuDeductReq.setPayLogId("aaabbbccc");

        ThirdPayResp thirdPayResp = BaoFuSingleHttpUtil.singleOrderRefund(baoFuDeductReq);
        System.out.println(JSONUtil.toJsonStr(thirdPayResp));
    }

}