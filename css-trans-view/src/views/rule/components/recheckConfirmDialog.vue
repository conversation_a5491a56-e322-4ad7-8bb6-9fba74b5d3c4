<template>
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialog" center :title="showTitle()" width="450px">
      <el-form ref="submitForm" :model="formData" :rules="rules" label-width="30%">
        <!-- 其他表单项 -->
        <el-form-item v-if="this.formData.status !== 'EFFECTIVE_WAIT_INVALIDITY'" label="是否立即生效" prop="effictiveFlag">
          <el-select v-model="formData.effictiveFlag" placeholder="请选择" style="width: 90%" clearable filterable @change="updateRules">
            <el-option v-for="item in effictiveFlags" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- 注意这里的 v-if 判断条件 -->
        <el-form-item v-if="this.formData.status !== 'EFFECTIVE_WAIT_INVALIDITY' && !formData.effictiveFlag" label="复核生效时间:" prop="recheckEffectiveTime">
          <el-date-picker style="width: 90%" v-model="formData.recheckEffectiveTime" type="datetime" placeholder="选择日期"></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialog = false">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">确 认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {recheck} from "@/api/rule/payRule.js";
export default {
  name: 'Dialog',
  data() {
    return {
      formData: {},
      ineffectiveTime: null,
      showEffectiveTime: false, // 控制是否显示复核生效时间的选择器
      effictiveFlags:[
        {
          label:'是',
          value:true
        },{
          label:'否',
          value:false
        }
      ],
      loading:false,
      dialog: false,
      rules: {
        payRuleDetailId: [{ required: true, message: '规则id不能为空', trigger: 'blur' }],
        effictiveFlag: [{ required: true, message: '请选择是否立即生效', trigger: 'blur' }],
        recheckEffectiveTime: []
      },
    }
  },
  methods: {
    showTitle(){
       return this.formData.status !== 'EFFECTIVE_WAIT_INVALIDITY' ?'生效复核确认':'失效复核确认'
    },
    updateRules(value) {
      if (value === false) { // 如果选择了 "否"
        const validateRecheckEffectiveTime = (rule, value, callback) => {
            const now = new Date();
            const selectedTime = new Date(value);
            if (this.ineffectiveTime != null &&  selectedTime >= this.ineffectiveTime){
              callback(new Error('复核生效时间必须早于规则失效时间'));
            }
            if (selectedTime <= now) {
              callback(new Error('复核生效时间必须晚于当前时间'));
            } else {
              callback();
            }
        };
        this.rules.recheckEffectiveTime = [
          { required: true, message: '请选择复核生效时间', trigger: 'blur' },
          { validator: validateRecheckEffectiveTime, trigger: 'blur' }
        ];
      } else {
        this.rules.recheckEffectiveTime = []; // 移除必填规则
      }
      // 重置表单验证状态以应用新的规则
      this.$nextTick(() => {
        this.$refs.submitForm.clearValidate();
      });
    },
    openDialog(payRuleDetailId,status,ineffectiveTime){
      this.ineffectiveTime =ineffectiveTime;
      this.formData={},
      this.formData.payRuleDetailId=payRuleDetailId;
      this.formData.status = status;
      if (this.formData.status === 'EFFECTIVE_WAIT_INVALIDITY' ){
        this.formData.effictiveFlag = true;
      }
      this.dialog = true;

    },
    submit(){
      this.$refs['submitForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          recheck(this.formData).then(resp => {
            if (resp.code === '200') {
              this.dialog = false
              this.loading = false
              this.$message.success('复核成功');
              this.$emit("closeDialog")
            }else {
              this.loading = false
              this.$message.warning(resp.message);
            }
          }).catch(resp =>{
            this.dialog=false
            this.loading=false
            this.$message.warning(resp.data)
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style>
.exple {
  height: 50px;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: left;
}
.exple .lineLeft {
  display: inline-block;
  width: 3%;
  border-top: 1px solid #ccc;
  height: 2px;
}
.exple .lineRight {
  display: inline-block;
  width: 82%;
  border-top: 1px solid #ccc;
  height: 2px;
}
.exple .txt {
  color: #686868;
  vertical-align: middle;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-weight: bold;
  margin: 0 15px;
}
</style>
