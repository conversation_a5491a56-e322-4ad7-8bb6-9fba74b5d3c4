package com.fb.css.common.constants;

import lombok.Getter;

/**
 *
 *
 * <AUTHOR>
 */
@Getter
public enum RpyType {
    // 按期还款 期供还款
    SCHEDULED_RPY(1, "期供还款"),
    //提前还当期
    EARLY_RPY_CURRENT_PERIOD(2, "提前还当期"),
    //提前结清
    EARLY_CLEAR(3, "提前结清"),
    //部分还款
    PARTIAL_RPY(4, "部分还款"),
    //单期代偿
    SINGLE_COMPENSATION(5, "单期代偿"),
    FULL_COMPENSATION(6, "整笔代偿"),
    /** 按金额还款 */
    AMOUNT_REPAY(6, "按金额还款")
    ;

    final int id;
    final String desc;

    RpyType(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }

}
