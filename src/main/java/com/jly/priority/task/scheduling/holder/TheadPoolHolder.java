package com.jly.priority.task.scheduling.holder;

import com.jly.priority.task.scheduling.exception.NotSupportException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2024/3/24 18:21
 * @Describe
 */
public class TheadPoolHolder {
    private TheadPoolHolder(){}
    /**
     * 所有队列对应的线程池集合
     */
    private static final Map<String,ThreadPoolTaskExecutor> QUEUE_THREAD_POOLS = new ConcurrentHashMap<>(8);
    /**
     * 注册队列对应的线程池
     * @param executeName executeName
     * @param executor executor
     */
    public static void register(String executeName, ThreadPoolTaskExecutor executor){
        if (executeName == null || executor == null) throw new NotSupportException("不支持空线程池的注册！");
        QUEUE_THREAD_POOLS.put(executeName,executor);
    }
    /**
     * 获取队列对应的执行线程池
     * @param executeName 队列名
     * @return 执行线程池
     */
    public static Optional<ThreadPoolTaskExecutor> getQueueExecutor(String executeName){return Optional.ofNullable(QUEUE_THREAD_POOLS.get(executeName));}
}
