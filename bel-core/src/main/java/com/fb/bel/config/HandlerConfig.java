package com.fb.bel.config;

import com.fb.bel.process.context.ChannelTransferQueryContext;
import com.fb.bel.process.context.ChannelTransferSubmitContext;
import com.fb.bel.process.context.TransferApplyContext;
import com.fb.bel.process.context.sync.SyncChannelTransferQueryContext;
import com.fb.bel.process.context.sync.SyncChannelTransferSubmitContext;
import com.fb.bel.process.context.sync.SyncTransferApplyContext;
import com.fb.bel.process.definition.BaseContext;
import com.fb.bel.process.handler.transfer.ChannelTransferQueryHandler;
import com.fb.bel.process.handler.transfer.ChannelTransferSubmitHandler;
import com.fb.bel.process.handler.transfer.TransferApplyHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/1 20:32
 * @Describe
 */
@Configuration
public class HandlerConfig {


    @Bean("defaultBaseContext")
    public BaseContext initHandler(Map<String, TransferApplyHandler> applyhandlerMap,
                                   Map<String, ChannelTransferSubmitHandler> submitHandlerMap,
                                   Map<String, ChannelTransferQueryHandler> queryHandlerMap){
        //申请
        TransferApplyContext.register(applyhandlerMap);
        //同步
        SyncTransferApplyContext.register(applyhandlerMap);
        //提交
        ChannelTransferSubmitContext.register(submitHandlerMap);
        //同步
        SyncChannelTransferSubmitContext.register(submitHandlerMap);
        //查询
        ChannelTransferQueryContext.registerQuery(queryHandlerMap);
        //同步
        SyncChannelTransferQueryContext.registerQuery(queryHandlerMap);
        //结果
        return new BaseContext();
    }
}
