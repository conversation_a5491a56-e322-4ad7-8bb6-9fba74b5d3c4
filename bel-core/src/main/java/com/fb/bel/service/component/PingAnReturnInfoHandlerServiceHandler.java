package com.fb.bel.service.component;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fb.bel.api.domain.*;
import com.fb.bel.dto.response.TransFlowInfoResp;
import com.fb.bel.dto.pingan.PingAnApiRequestDTO;
import com.fb.bel.dto.pingan.PingAnApiResultDataDTO;
import com.fb.bel.dto.pingan.resp.PingAnTransFlowResp;
import com.fb.bel.dto.request.TransFlowInfoReq;
import com.fb.bel.dto.request.TransFlowInfoReqDTO;
import com.fb.bel.dto.result.PingAnExtData;
import com.fb.bel.dto.result.PingAnAccountItemResp;
import com.fb.bel.api.enums.BelChannelEnum;
import com.fb.bel.mapstruct.PingAnExtDataMapper;
import com.fb.bel.utils.PingAnUtil;
import com.fb.css.common.exception.ServerInnerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

import static com.fb.bel.constants.PingAnConstants.*;
import static com.fb.bel.enums.ThirdReqResultStatus.SUCCESS;
import static com.fb.bel.factory.ServiceFactory.THREE_PARAM_FMT;
import static com.fb.css.common.constants.Constants.SPACE;
import static com.fb.css.common.constants.Constants.SYS;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PingAnReturnInfoHandlerServiceHandler extends AbstractReturnInfoHandlerServiceHandler {

    PingAnExtDataMapper pingAnExtDataMapper = PingAnExtDataMapper.INSTANCE;

    @Override
    public BelChannelEnum getBizCode() {
        return BelChannelEnum.PING_AN;
    }

    @Override
    protected TransFlowInfoResp<TransFlowInfo> queryTransFlowInfoPage(TransFlowInfoReqDTO transFlowInfoReqDTO,
                                                                      TransQueryBatch batchInfo) {
        PingAnApiRequestDTO pingAnApiRequestDTO = getTransFlowInfoPingAnApiRequestDTO(transFlowInfoReqDTO);
        PingAnApiResultDataDTO resultDataDTO = PingAnUtil.request(pingAnApiRequestDTO, QUERY_ACCOUNT_ITEM_INTERFACE_NAME);
        if(SUCCESS.equals(resultDataDTO.getReqResultStatus())){
            PingAnTransFlowResp resp = JSON.parseObject(resultDataDTO.getData(), PingAnTransFlowResp.class);
            // 包装返回数据
            List<TransFlowInfo> infoList = resp.getRecords().stream().map(accountItem ->
                    getTransFlowInfo(transFlowInfoReqDTO, batchInfo, accountItem)
            ).collect(Collectors.toList());
            TransFlowInfoResp<TransFlowInfo> transFlowInfoResp = new TransFlowInfoResp<>();
            transFlowInfoResp.setPageNo(resp.getPageNo());
            transFlowInfoResp.setPageSize(resp.getPageSize());
            transFlowInfoResp.setTotal(resp.getTotal());
            transFlowInfoResp.setRecords(infoList);
            transFlowInfoResp.setStatus(resultDataDTO.getReqResultStatus());
            transFlowInfoResp.setMessage(resultDataDTO.getMessage());
            return transFlowInfoResp;
        }
        TransFlowInfoResp<TransFlowInfo> transFlowInfoResp = new TransFlowInfoResp<>();
        transFlowInfoResp.setStatus(resultDataDTO.getReqResultStatus());
        transFlowInfoResp.setMessage(resultDataDTO.getMessage());
        return transFlowInfoResp;
    }

    @Override
    public long queryBatchTotal(TransFlowInfoReqDTO transFlowInfoReqDTO, TransferChannelAccount account) {
        PingAnApiRequestDTO pingAnApiRequestDTO = getTransFlowInfoPingAnApiRequestDTO(transFlowInfoReqDTO);
        PingAnApiResultDataDTO resultDataDTO = PingAnUtil.request(pingAnApiRequestDTO, QUERY_ACCOUNT_ITEM_INTERFACE_NAME);
        if(!SUCCESS.equals(resultDataDTO.getReqResultStatus())){
            throw new ServerInnerException("请求查询批次总条数失败");
        }
        PingAnTransFlowResp resp = JSON.parseObject(resultDataDTO.getData(), PingAnTransFlowResp.class);
        return resp.getTotal();
    }

    private PingAnApiRequestDTO getTransFlowInfoPingAnApiRequestDTO(TransFlowInfoReqDTO transFlowInfoReqDTO) {
        PingAnApiRequestDTO pingAnApiRequestDTO = new PingAnApiRequestDTO();
        pingAnApiRequestDTO.setAccount(transFlowInfoReqDTO.getAccount());
        pingAnApiRequestDTO.setChannel(transFlowInfoReqDTO.getChannel());
        TransFlowInfoReq transFlowInfoReq = new TransFlowInfoReq();
        transFlowInfoReq.setPageSize(transFlowInfoReqDTO.getPageSize());
        transFlowInfoReq.setPageNo(transFlowInfoReqDTO.getPageNo());
        transFlowInfoReq.setPageSize(transFlowInfoReqDTO.getPageSize());
        transFlowInfoReq.setBeginDate(DateUtil.format(transFlowInfoReqDTO.getBeginDate(), DATE_FORMAT));
        transFlowInfoReq.setEndDate(DateUtil.format(transFlowInfoReqDTO.getEndDate(), DATE_FORMAT));
        JSONObject requestBody = (JSONObject) JSON.toJSON(transFlowInfoReq);
        pingAnApiRequestDTO.setRequestBody(requestBody);
        return pingAnApiRequestDTO;
    }

    private TransFlowInfo getTransFlowInfo(TransFlowInfoReqDTO transFlowInfoReqDTO, TransQueryBatch batchInfo,
                                           PingAnAccountItemResp accountItem) {
        TransFlowInfo returnInfo = new TransFlowInfo();
        returnInfo.setCnfAccountId(transFlowInfoReqDTO.getAccount().getId());
        returnInfo.setAccount(accountItem.getAccount());
        returnInfo.setChannelNo(transFlowInfoReqDTO.getChannel().getChannelNo());
        returnInfo.setTransUniqueNo(accountItem.getId());
        returnInfo.setAccountName(accountItem.getAccountName());
        returnInfo.setBankCode(transFlowInfoReqDTO.getChannel().getChannelNo());
        returnInfo.setBatchNo(batchInfo.getBatchNo());
        returnInfo.setBankSeqNo(accountItem.getBankBusinessId());
        returnInfo.setBankTradeNo(accountItem.getBusinessOrderNo());
        returnInfo.setBalance((new BigDecimal(accountItem.getBalance())).setScale(4, RoundingMode.HALF_UP));
        returnInfo.setAmount((new BigDecimal(accountItem.getAmount())).setScale(4, RoundingMode.HALF_UP));
        String tradeDateTime = THREE_PARAM_FMT.get().format(new String[]{accountItem.getTradeDate(), SPACE, accountItem.getTradeTime()});
        returnInfo.setTradeDatetime(DateUtil.parse(tradeDateTime, DATE_FORMAT));
        returnInfo.setTradeFlag(accountItem.getLoanType());
        returnInfo.setCounterpartyAccount(accountItem.getOtherPartyAccount());
        returnInfo.setCounterpartyName(accountItem.getOtherPartyAccountName());
        returnInfo.setCounterpartyBank(accountItem.getOtherPartyBankName());
        returnInfo.setSummary(accountItem.getSummary());
        returnInfo.setRemark(accountItem.getRemark());
        PingAnExtData extData = pingAnExtDataMapper.convertFromPingAnAccountItemResp(accountItem);
        returnInfo.setBankBizId(accountItem.getBusinessId());
        returnInfo.setExtData(JSON.toJSONString(extData));
        returnInfo.setCreatedBy(SYS);
        return returnInfo;
    }
}
