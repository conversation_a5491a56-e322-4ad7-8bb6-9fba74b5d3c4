package com.fb.css.trans.api.dto.oms;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RuleTypeConfigDetailCopyDTO implements Serializable {
    private static final long serialVersionUID = 6224513471507542507L;

    /**
     * 订单序号
     */
    private Integer orderIndex;

    /**
     * 详细名称
     */
    private String ruleTypeDetailName;

    /**
     * 脚本 code
     */
    private String scriptCode;

    /**
     * 清分订单的 rule code
     */
    private String clearingRuleTypeConfigCode;

    /**
     * 空中清分订单的 rule code
     */
    private String airClearingRuleTypeConfigCode;

    /**
     * 交易订单的数据
     */
    private String transData;

    /**
     * 清分订单的数据
     */
    private String clearingData;

    private String airClearingData;
}
