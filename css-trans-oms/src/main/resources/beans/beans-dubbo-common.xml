<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
      http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<dubbo:application name="${dubbo.application.name}" />
	<dubbo:registry id="registry" file=".dubbo/${dubbo.application.name}.cache" protocol="dubbo" address="${dubbo.registry.address}" group="${dubbo.registry.root.node}" />
	<dubbo:protocol name="dubbo" port="${dubbo.protocol.port}" threads="${dubbo.pool.threads}" threadpool="smycached" />

</beans>


