package com.fb.spec.finacial.allocation.core.dao.entity;

import com.fb.framework.base.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class VarDetail extends BaseEntity implements Serializable {
    /** 主键 */
    private String id;

    /** 项目号 */
    private String projectNo;

    /** 信托计划编码 */
    private String trustPlanCode;

    /** 业务订单号 */
    private String bizOrderId;

    /** 业务订单完成时间 */
    private Date bizCompletionDatetime;

    /** 变量 key */
    private String varKey;

    /** 变量名称 */
    private String varName;

    /** 获取的来源key（或费用项） */
    private String sourceKey;

    /** 来源 key 对应的值 */
    private BigDecimal sourceValue;

    /** 计提的日期 20231224
 */
    private Date accrualDatetime;

    /** 创建时间 */
    private Date createDatetime;

    /** 修改时间 */
    private Date updateDatetime;

    /** 创建者 */
    private String createdBy;

    /** 修改者 */
    private String updatedBy;

    /** 扩展字段 */
    private String extData;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo == null ? null : projectNo.trim();
    }

    public String getTrustPlanCode() {
        return trustPlanCode;
    }

    public void setTrustPlanCode(String trustPlanCode) {
        this.trustPlanCode = trustPlanCode == null ? null : trustPlanCode.trim();
    }

    public String getBizOrderId() {
        return bizOrderId;
    }

    public void setBizOrderId(String bizOrderId) {
        this.bizOrderId = bizOrderId == null ? null : bizOrderId.trim();
    }

    public Date getBizCompletionDatetime() {
        return bizCompletionDatetime;
    }

    public void setBizCompletionDatetime(Date bizCompletionDatetime) {
        this.bizCompletionDatetime = bizCompletionDatetime;
    }

    public String getVarKey() {
        return varKey;
    }

    public void setVarKey(String varKey) {
        this.varKey = varKey == null ? null : varKey.trim();
    }

    public String getVarName() {
        return varName;
    }

    public void setVarName(String varName) {
        this.varName = varName == null ? null : varName.trim();
    }

    public String getSourceKey() {
        return sourceKey;
    }

    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey == null ? null : sourceKey.trim();
    }

    public BigDecimal getSourceValue() {
        return sourceValue;
    }

    public void setSourceValue(BigDecimal sourceValue) {
        this.sourceValue = sourceValue;
    }

    public Date getAccrualDatetime() {
        return accrualDatetime;
    }

    public void setAccrualDatetime(Date accrualDatetime) {
        this.accrualDatetime = accrualDatetime;
    }

    public Date getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }

    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData == null ? null : extData.trim();
    }
}