package com.fb.spec.finacial.allocation.core.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fb.spec.finacial.allocation.core.dao.entity.AccrualOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【t_accrual_order(计提订单表)】的数据库操作Mapper
* @createDate 2024-02-29 15:05:23
* @Entity generator.domain.TAccrualOrder
*/
@Mapper
public interface AccrualOrderDao extends BaseMapper<AccrualOrder> {

    AccrualOrder getAccrualOrderById(@Param("id") String id);
}




