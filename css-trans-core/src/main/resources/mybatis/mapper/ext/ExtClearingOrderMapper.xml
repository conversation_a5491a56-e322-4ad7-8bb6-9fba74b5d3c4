<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fb.css.trans.dao.ext.ExtClearingOrderDao">
    <resultMap id="BaseResultMap" type="com.fb.css.trans.dao.entity.ClearingOrder">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="gray_version" jdbcType="VARCHAR" property="grayVersion" />
        <result column="trans_order_id" jdbcType="VARCHAR" property="transOrderId" />
        <result column="front_trans_no" jdbcType="VARCHAR" property="frontTransNo" />
        <result column="clearing_num" jdbcType="INTEGER" property="clearingNum" />
        <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
        <result column="account_name" jdbcType="VARCHAR" property="accountName" />
        <result column="trans_plan_code" jdbcType="VARCHAR" property="transPlanCode" />
        <result column="clearing_amount" jdbcType="DECIMAL" property="clearingAmount" />
        <result column="clearing_status" jdbcType="VARCHAR" property="clearingStatus" />
        <result column="trans_type" jdbcType="VARCHAR" property="transType" />
        <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
        <result column="fee_type" jdbcType="VARCHAR" property="feeType" />
        <result column="order_type" jdbcType="VARCHAR" property="orderType" />
        <result column="related_id" jdbcType="VARCHAR" property="relatedId" />
        <result column="pay_finish_time" jdbcType="TIMESTAMP" property="payFinishTime" />
        <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime" />
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="asset_org_no" jdbcType="VARCHAR" property="assetOrgNo" />
        <result column="cap_no" jdbcType="VARCHAR" property="capNo" />
        <result column="repay_channel" jdbcType="VARCHAR" property="repayChannel" />
        <result column="scenes" jdbcType="VARCHAR" property="scenes" />
        <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fb.css.trans.dao.entity.ClearingOrder">
        <result column="clearing_ext_data" jdbcType="LONGVARCHAR" property="clearingExtData" />
    </resultMap>
    <sql id="Base_Column_List">
        id, project_no, project_name, gray_version, trans_order_id, front_trans_no, clearing_num,
        account_no, account_name, trans_plan_code, clearing_amount, clearing_status, trans_type,
        biz_type, fee_type, order_type, related_id, pay_finish_time, completion_time, create_datetime,
        update_datetime, created_by, updated_by, asset_org_no, cap_no, repay_channel, scenes,
        pay_channel
    </sql>
    <sql id="Blob_Column_List">
        clearing_ext_data
    </sql>
    <update id="updateStatus" parameterType="com.fb.css.trans.dto.ClearingOrderUpdateReq">
        UPDATE t_clearing_order
        SET
        clearing_status = #{toStatus},
        update_datetime = #{updateDatetime},
        updated_by = #{updatedBy}
        <if test="completionTime != null">
            ,completion_time = #{completionTime}
        </if>
        <if test="accountNo != null and accountNo != ''">
            ,account_no = #{accountNo}
        </if>
        WHERE id = #{id}
        <if test="toStatus != 'SUCCESS'">
            AND clearing_status = #{fromStatus}
        </if>
    </update>
    <select id="selectByTransOrderIdAndClearingNum" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from t_clearing_order
        where trans_order_id = #{transOrderId,jdbcType=VARCHAR}
        AND clearing_num = #{clearingNum,jdbcType=NUMERIC}
    </select>
    <select id="countByTransOrderId" resultType="java.lang.Integer">
        select
            count(1)
        from t_clearing_order
        where trans_order_id = #{transOrderId,jdbcType=VARCHAR}
        and clearing_status != 'SUCCESS'
    </select>
    <select id="selectBatchRetryOrder" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from t_clearing_order force index(`idx_status_id`)
        where clearing_status = #{expectStatus}
        and id > #{startId}
        order by id limit #{batchSize}
    </select>
    <select id="selectByTransOrderId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from t_clearing_order
        where trans_order_id = #{transOrderId,jdbcType=VARCHAR}
    </select>

    <select id="selectByFrontTransNo" resultType="com.fb.css.trans.dao.entity.ClearingOrder">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from t_clearing_order
        where front_trans_no = #{frontTransNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByFrontTransNoList" resultType="com.fb.css.trans.dao.entity.ClearingOrder">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from t_clearing_order
        where front_trans_no in
        <foreach collection="frontTransNoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <sql id="clearingOrderQuereWhere">
        <if test=" fbNo !=null  and fbNo != '' ">
            and fb_no = #{fbNo,jdbcType=VARCHAR}
        </if>
        <if test=" id !=null  and id != '' ">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test=" projectNo !=null  and projectNo != '' ">
            and project_no = #{projectNo,jdbcType=VARCHAR}
        </if>
        <if test=" transOrderId !=null  and transOrderId != '' ">
            and trans_order_id = #{transOrderId,jdbcType=VARCHAR}
        </if>
        <if test=" frontTransNo !=null  and frontTransNo != '' ">
            and front_trans_no = #{frontTransNo,jdbcType=VARCHAR}
        </if>
        <if test=" clearingStatus !=null  and clearingStatus != '' ">
            and clearing_status = #{clearingStatus,jdbcType=VARCHAR}
        </if>
        <if test=" orderTypeList !=null and orderTypeList.size >0  ">
            and order_type in
            <foreach close=")" collection="orderTypeList" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" transType !=null  and transType != '' ">
            and trans_type = #{transType,jdbcType=VARCHAR}
        </if>
        <if test=" bizType !=null  and bizType != '' ">
            and biz_type = #{bizType,jdbcType=VARCHAR}
        </if>
        <if test="createDatetimeStr != null ">
            and create_datetime >= #{createDatetimeStr,jdbcType=TIMESTAMP}
        </if>
        <if test="createDatetimeEnd != null ">
            AND create_datetime &lt;= #{createDatetimeEnd,jdbcType=TIMESTAMP}
        </if>

    </sql>
    <select id="totalCount" resultType="int">
        select  count(1) from  t_clearing_order
        where 1=1
        <include refid="clearingOrderQuereWhere"/>
    </select>

    <select id="listByPage" resultType="com.fb.css.trans.api.dto.resp.ClearingOrderResp">
        select  id, project_no, project_name, trans_order_id, clearing_num, account_no, account_name, clearing_amount,
        clearing_status, trans_type, biz_type, fee_type, clearing_ext_data, completion_time, create_datetime, update_datetime, created_by,
        updated_by,  order_type, related_id, front_trans_no, pay_finish_time, trans_plan_code
        from  t_clearing_order
        where 1=1
        <include refid="clearingOrderQuereWhere"/>
        order by id desc
        limit  #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

</mapper>
