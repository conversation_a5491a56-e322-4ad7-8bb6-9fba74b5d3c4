package com.fb.css.trans.utils;

import cn.hutool.core.util.RandomUtil;
import com.fb.css.common.constants.Constants;
import com.fb.css.common.constants.NumberConstants;
import com.fb.css.common.util.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class RuleCopyUtil {

    private static final Pattern PATTERN = Pattern.compile("(\\d{4})(\\d{2})(\\d{2})");

    private static final Pattern PATTERN_DDD = Pattern.compile("(\\d{4})(\\d{2})(\\d{2})(_\\d{3})");

    private RuleCopyUtil() {
    }

    public static String getNewRuleCode(String randomInt, String ruleCode) {
        if (StringUtils.isBlank(ruleCode)) {
            return StringUtils.EMPTY;
        }
        randomInt = StringUtils.leftPad(randomInt, NumberConstants.INT_3, "0");
        // 获取当前日期
        String currentDate = DateTimeUtil.formatDate(new Date(), DateTimeUtil.YYYYMMDD) + Constants.UNDERLINE + randomInt;
        // 正则表达式匹配日期部分
        Matcher matcher = PATTERN_DDD.matcher(ruleCode);
        if (matcher.find()) {
            // 替换日期
            return matcher.replaceAll(currentDate);
        }
        matcher = PATTERN.matcher(ruleCode);
        if (matcher.find()) {
            // 替换日期
            return matcher.replaceAll(currentDate);
        }
        return ruleCode;
    }


    public static String getNewRuleCode(String projectNo, String targetProjectNo, String randomInt, String ruleCode) {
        String newRuleCode = getNewRuleCode(randomInt, ruleCode);

        String p1 = projectNo.toUpperCase().replace(Constants.HYPHEN, Constants.UNDERLINE);
        String p2 = targetProjectNo.toUpperCase().replace(Constants.HYPHEN, Constants.UNDERLINE);
        return newRuleCode.replace(p1, p2);
    }

    public static String replaceProjectName(String str, String projectName, String targetProjectName) {
        if (StringUtils.isEmpty(str)) {
            return StringUtils.EMPTY;
        }
        return str.replace(projectName, targetProjectName);
    }

    public static void main(String[] args) {
        String ruleCode = "HF_ZXD_FMYH_20241028_000_TEST";
        System.out.println(getNewRuleCode("hf-zxd-fmyh", "hnxh-zxd-zbyh", "001", ruleCode));

        String scriptCode = "HF_ZXD_FMYH_TEST";
        System.out.println(getNewRuleCode("hf-zxd-fmyh", "hnxh-zxd-zbyh", "001", scriptCode));
    }
}
