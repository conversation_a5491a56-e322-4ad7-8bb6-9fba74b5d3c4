package com.fb.css.trans.dto.rules;

import com.alibaba.fastjson.JSONObject;
import com.fb.css.common.constants.ExpenditureWay;
import com.fb.css.trans.dto.rules.json.ExtAttributeConfigDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;


/**
 * 交易拆分规则脚本运行结果
 *
 * <AUTHOR>
 */
@Data
public class TransOrderRuleResult {

    /**
     * 序号（也就是支付系统的顺序）
     */
    private int index;

    /**
     * 扣款主体
     */
    private String subject;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 使用的费用项
     */
    private Map<String, BigDecimal> useFeeInfo;

    /**
     * 是否要请求支付系统
     */
    private boolean requestPy = false;

    /**
     * 是否要支付空中清分
     */
    private boolean airClearing = false;

    /**
     * 交易失败是否要发起退款（默认 需要 true)
     */
    private boolean failureNeedRefund = true;

    /**
     * 是否需要清分
     */
    private boolean needClearing = true;

    /**
     * 交易金额为零，是否也要做清分（默认不需要）
     */
    private boolean zeroAmountNeedClearing = false;

    /**
     * 交易订单支付方式
     */
    private ExpenditureWay expenditureWay;

    /**
     * 扣款商户号
     */
    private String merchantNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付空中清分的 rule Code
     */
    private String airClearingRuleCode;

    /**
     * 清分订单的 rule code
     */
    private String clearingRuleCode;

    /**
     * 三方协议支付类型
     */
    private String thirdAgreementPayType;
    /** 是否按序号处理（是Y,  否N) */
    private String paySerial;

    /**
     * 脚本唯一编码
     */
    private String scriptCode;

    /**
     * 脚本内容
     */
    private String script;

    /**
     * cap 的 0 元是否需要请求(默认为true，false:不需要请求)
     */
    private Boolean zeroAmountNeedRequest;

    /**
     * 扩展属性配置
     */
    private Map<String, ExtAttributeConfigDTO> extAttributeConfigs;

    /**
     * 交易订单的扩展数据
     */
    private JSONObject transExtData;

    public String getSubject() {
        return subject.trim();
    }
    public void setAirClearingRuleCode(String airClearingRuleCode) {
        if (this.airClearing) {
            this.airClearingRuleCode = airClearingRuleCode;
        }
    }
}
