package com.fb.css.trans.dao.entity;

import com.fb.css.apt.annotation.HistoryTableAutoGenerate;
import com.fb.framework.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

@HistoryTableAutoGenerate(targetPackage = "com.fb.css.trans.dao.history", statusFiled = "orderStatus", statusValues = {"SUCCESS", "FAIL", "PART_SUCCESS"})
@EqualsAndHashCode(callSuper = false)
@Data
public class BizOrderDetail extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 720022235932835984L;

    /** 主键id */
    private String id;

    /** 业务订单id */
    private String bizOrderId;

    /** 订单序号 */
    private Integer orderNum;

    /** 上游还款流水号 */
    private String frontTransNo;

    /** 上游支付流水号 */
    private String frontTransIndexId;

    /** 关联的业务id */
    private String relatedId;

    /** 借款订单号 */
    private String loanOrderId;

    /** 项目编码 */
    private String projectNo;

    /** 项目名称 */
    private String projectName;

    /** 信托计划编码 */
    private String trustPlanCode;

    /** 灰度版本 */
    private String grayVersion;

    /** 上游来源系统 */
    private String sysCode;

    /** 状态： （处理中、成功、失败） */
    private String orderStatus;

    /** 订单类型：NORMAL（默认） REVERSAL 冲正 */
    private String orderType;

    /** 完成时间 */
    private Date completionTime;

    /** 按序号处理（是Y,  否N) */
    private String paySerial;

    /** 优先级策略 */
    private String priorityStrategy;

    /** 必须全部成功（是Y、否N) */
    private String allNeedSuccess;

    /** 支付规则编码 */
    private String ruleCode;

    /** 创建人 */
    private String createdBy;

    /** 更新人 */
    private String updatedBy;

    /** 创建时间 */
    private Date createDatetime;

    /** 更新时间 */
    private Date updateDatetime;

    /** 资产方 */
    private String assetOrgNo;

    /** 资金方 */
    private String capNo;

    /** 还款渠道来源 1-资产APP，2-资产客服，3-资产跑批，4-融担客服，5-融担跑批，6-催收客服，7-催收跑批，8-线下溢缴，9- app-xly */
    private String repayChannel;

    /** 支付还款场景 */
    private String scenes;

    /** 扩展信息 */
    private String extData;
}