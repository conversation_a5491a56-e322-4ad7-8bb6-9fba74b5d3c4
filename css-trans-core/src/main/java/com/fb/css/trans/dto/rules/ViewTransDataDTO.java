package com.fb.css.trans.dto.rules;

import com.fb.css.trans.dto.rules.json.ExtAttributeConfigDTO;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ViewTransDataDTO {
    /**
     * 交易主体
     */
    private String subject;
    /**
     * 交易订单支付方式 CBS 资金方， GATEWAY 网关
     */
    private String expenditureWay;
    /**
     * 是否要请求支付系统
     */
    private Boolean requestPy;
    /**
     * cap 的 0 元是否需要请求(默认为true，false:不需要请求)
     */
    private Boolean zeroAmountNeedRequest;
    /**
     * 交易失败是否要发起退款，默认需要
     */
    private Boolean failureNeedRefund;
    /**
     * 交易金额为零，是否也要做清分（默认不需要）
     */
    private Boolean zeroAmountNeedClearing;
    /**
     * 是否需要支付空中清分
     */
    private Boolean airClearing;
    /**
     * 是否需要清分（默认需要）
     */
    private Boolean needClearing;
    /**
     * 扩展属性
     */
    private Map<String, ExtAttributeConfigDTO> extAttributeConfigs;
}
