package com.fb.css.trans.dao.entity.slave;

import com.fb.framework.base.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class TCssExtTransOrder extends BaseEntity implements Serializable {
    /**主键*/
    private String id;

    /**业务订单号*/
    private String bizOrderId;

    /**交易订单序列（区分1笔业务，生成交易订单的序列，从1开始99结束）
退款订单：则是按关联的交易订单序号x100+1开始计算：如101、9901，序号范围1-99
*/
    private Integer transNum;

    /**关联的原交易订单id（退款场景）
*/
    private String relatedId;

    /**客户号*/
    private String custNo;

    /**客户姓名*/
    private String custName;

    /**清分使用的规则编码*/
    private String clearingRuleCode;

    /**灰度版本*/
    private String grayVersion;

    /**项目名称*/
    private String projectName;

    /**项目编码*/
    private String projectNo;

    /**信托计划编码*/
    private String trustPlanCode;

    /**交易金额*/
    private BigDecimal transAmount;

    /**业务类型：放款 还款 代偿、退款*/
    private String bizType;

    /**是否退款 Y N*/
    private String isRefundTrans;

    /**状态：按业务类型区分：
根据业务类型：
代偿（有资金流）: 待处理、支付中、支付失败、支付成功
代偿（记账）：
待处理、代偿成功
还款： 待处理、支付中、支付失败、支付成功
放款：待处理、放款成功
计提：待处理、计提成功
退款：待处理、退款中、退款成功、退款失败
交易状态*/
    private String transStatus;

    /**是否支付：Y N*/
    private String isReqPyt;

    /**空中清分规则编码*/
    private String airPytRuleCode;

    /**还款期次*/
    private Integer rpyPeriod;

    /**支付签约协议号*/
    private String cardAgreementNumber;

    /**还款方式（线上还款 ONLINE、线下还款OFFLINE）*/
    private String rpyWay;

    /**还款类型（按期还款、提前还当期、提前结清、部分还款）*/
    private String rpyType;

    /**身份证号*/
    private String idCardNumber;

    /**支付渠道id*/
    private String channelId;

    /**支付通道用户号*/
    private String channelUserNo;

    /**银行卡号*/
    private String bankCardNo;

    /**银行预留手机号*/
    private String mobile;

    /**银行编码*/
    private String bankCode;

    /**还款场景*/
    private String scenes;

    /**资金方*/
    private String capNo;

    /**资产方*/
    private String assetOrgNo;

    /**反担保方*/
    private String counterGuarantor;

    /**完成时间*/
    private Date completionTime;

    /**扣款主体*/
    private String subject;

    /**创建时间*/
    private Date createDatetime;

    /**更新时间*/
    private Date updateDatetime;

    /**创建人*/
    private String createdBy;

    /**更新人*/
    private String updatedBy;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBizOrderId() {
        return bizOrderId;
    }

    public void setBizOrderId(String bizOrderId) {
        this.bizOrderId = bizOrderId == null ? null : bizOrderId.trim();
    }

    public Integer getTransNum() {
        return transNum;
    }

    public void setTransNum(Integer transNum) {
        this.transNum = transNum;
    }

    public String getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(String relatedId) {
        this.relatedId = relatedId == null ? null : relatedId.trim();
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo == null ? null : custNo.trim();
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public String getClearingRuleCode() {
        return clearingRuleCode;
    }

    public void setClearingRuleCode(String clearingRuleCode) {
        this.clearingRuleCode = clearingRuleCode == null ? null : clearingRuleCode.trim();
    }

    public String getGrayVersion() {
        return grayVersion;
    }

    public void setGrayVersion(String grayVersion) {
        this.grayVersion = grayVersion == null ? null : grayVersion.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo == null ? null : projectNo.trim();
    }

    public String getTrustPlanCode() {
        return trustPlanCode;
    }

    public void setTrustPlanCode(String trustPlanCode) {
        this.trustPlanCode = trustPlanCode == null ? null : trustPlanCode.trim();
    }

    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    public String getIsRefundTrans() {
        return isRefundTrans;
    }

    public void setIsRefundTrans(String isRefundTrans) {
        this.isRefundTrans = isRefundTrans == null ? null : isRefundTrans.trim();
    }

    public String getTransStatus() {
        return transStatus;
    }

    public void setTransStatus(String transStatus) {
        this.transStatus = transStatus == null ? null : transStatus.trim();
    }

    public String getIsReqPyt() {
        return isReqPyt;
    }

    public void setIsReqPyt(String isReqPyt) {
        this.isReqPyt = isReqPyt == null ? null : isReqPyt.trim();
    }

    public String getAirPytRuleCode() {
        return airPytRuleCode;
    }

    public void setAirPytRuleCode(String airPytRuleCode) {
        this.airPytRuleCode = airPytRuleCode == null ? null : airPytRuleCode.trim();
    }

    public Integer getRpyPeriod() {
        return rpyPeriod;
    }

    public void setRpyPeriod(Integer rpyPeriod) {
        this.rpyPeriod = rpyPeriod;
    }

    public String getCardAgreementNumber() {
        return cardAgreementNumber;
    }

    public void setCardAgreementNumber(String cardAgreementNumber) {
        this.cardAgreementNumber = cardAgreementNumber == null ? null : cardAgreementNumber.trim();
    }

    public String getRpyWay() {
        return rpyWay;
    }

    public void setRpyWay(String rpyWay) {
        this.rpyWay = rpyWay == null ? null : rpyWay.trim();
    }

    public String getRpyType() {
        return rpyType;
    }

    public void setRpyType(String rpyType) {
        this.rpyType = rpyType == null ? null : rpyType.trim();
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber == null ? null : idCardNumber.trim();
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId == null ? null : channelId.trim();
    }

    public String getChannelUserNo() {
        return channelUserNo;
    }

    public void setChannelUserNo(String channelUserNo) {
        this.channelUserNo = channelUserNo == null ? null : channelUserNo.trim();
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo == null ? null : bankCardNo.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getScenes() {
        return scenes;
    }

    public void setScenes(String scenes) {
        this.scenes = scenes == null ? null : scenes.trim();
    }

    public String getCapNo() {
        return capNo;
    }

    public void setCapNo(String capNo) {
        this.capNo = capNo == null ? null : capNo.trim();
    }

    public String getAssetOrgNo() {
        return assetOrgNo;
    }

    public void setAssetOrgNo(String assetOrgNo) {
        this.assetOrgNo = assetOrgNo == null ? null : assetOrgNo.trim();
    }

    public String getCounterGuarantor() {
        return counterGuarantor;
    }

    public void setCounterGuarantor(String counterGuarantor) {
        this.counterGuarantor = counterGuarantor == null ? null : counterGuarantor.trim();
    }

    public Date getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(Date completionTime) {
        this.completionTime = completionTime;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    public Date getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }

    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }
}